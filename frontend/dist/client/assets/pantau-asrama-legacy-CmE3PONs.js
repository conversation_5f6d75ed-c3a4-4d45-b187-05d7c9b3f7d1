System.register(["./jspdf.es.min-legacy-CbXTx0QS.js","./jspdf.plugin.autotable-legacy-fe79WZ9r.js","./vendor-legacy-BmDVBcfe.js","./index-legacy-B2X8pP_9.js"],function(t,e){"use strict";var a,i,n,r,l,o,s,d,h,p,c,g,m,f;return{setters:[t=>{a=t.E},t=>{i=t.a},t=>{n=t.c,r=t.a,l=t.t,o=t.m,s=t.s,d=t.F,h=t.p,p=t.v,c=t.q,g=t.k,m=t.o},t=>{f=t._}],execute:function(){var e=document.createElement("style");e.textContent="body{margin:20px;background-color:#f0f2f5;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif}h1{font-size:2.5em;text-align:center;margin-bottom:20px}.filter-item{display:flex;flex-direction:column;gap:15px;background-color:#fff;padding:20px;box-shadow:0 4px 8px rgba(0,0,0,.1);border-radius:20px;margin-bottom:20px;max-width:550px;width:100%;margin-left:auto;margin-right:auto}.filter-item div{display:flex;flex-direction:column}.filter-item label{margin-bottom:5px;font-weight:700}select,input,button{border:1px solid #ccc;padding:10px;font-size:16px;background-color:#f9f9f9;-webkit-appearance:none;appearance:none;border-radius:20px;box-sizing:border-box;width:100%;height:45px;min-height:44px;touch-action:manipulation}select,input{margin-top:0;margin-bottom:0}button{width:auto;margin:20px 10px;padding:0 20px;display:inline-block;cursor:pointer;height:50px}.table-container{width:100%;max-width:768px;overflow-x:auto;border:1px solid #ddd;border-radius:10px;margin:0 auto;-webkit-overflow-scrolling:touch;scrollbar-width:thin}table{width:100%;border-collapse:collapse;table-layout:auto;min-width:100%}th,td{padding:15px;text-align:left;border-bottom:1px solid #ddd;white-space:nowrap}th{background-color:#f4f4f4;cursor:pointer;position:relative;user-select:none;transition:background-color .2s}th:hover{background-color:#e0e0e0}th span{margin-left:5px;color:#666}th:first-child,th:nth-child(2){position:sticky;left:0;background-color:#fff;z-index:20;box-shadow:2px 0 4px rgba(0,0,0,.1)}td:first-child,td:nth-child(2){position:sticky;left:0;background-color:#fff;z-index:10;box-shadow:2px 0 4px rgba(0,0,0,.1)}tr:last-child td{border-bottom:none}th:first-child{cursor:default}th:first-child:hover{background-color:#f4f4f4}.button-container{text-align:center;margin-top:20px;width:100%;max-width:550px;margin-left:auto;margin-right:auto}@media (max-width: 768px){.filter-item{flex-direction:column;max-width:100%}button{width:100%}}@media (max-width: 480px){body{margin:10px}h1{font-size:2em;margin-bottom:15px}.filter-item{padding:15px;gap:12px;margin-bottom:15px}.table-container{font-size:14px}th,td{padding:8px 5px;font-size:13px}th:first-child,td:first-child{width:40px;min-width:40px}button{height:48px;font-size:16px;margin:10px 5px}.button-container{margin-top:15px}.button-container button{display:block;width:100%;margin:8px 0}}@media (max-width: 414px){h1{font-size:1.8em}.filter-item{padding:12px;border-radius:15px}select,input{height:48px;font-size:16px;padding:12px}.table-container{font-size:13px;border-radius:8px}th,td{padding:6px 3px;font-size:12px}th:first-child,td:first-child{width:35px;min-width:35px}th:nth-child(2),td:nth-child(2){min-width:60px}th:nth-child(3),td:nth-child(3){min-width:110px}th:nth-child(4),td:nth-child(4){min-width:70px}th:nth-child(5),td:nth-child(5){min-width:90px}th:nth-child(6),td:nth-child(6){min-width:80px}button{height:50px;margin:8px 0;border-radius:15px}}@media (max-width: 375px){h1{font-size:1.6em;margin-bottom:10px}.filter-item{padding:10px;gap:10px}.filter-item label{font-size:14px}select,input{height:46px;font-size:15px;padding:10px}.table-container{font-size:12px}th,td{padding:5px 2px;font-size:11px}th:first-child,td:first-child{width:30px;min-width:30px}th:nth-child(2),td:nth-child(2){min-width:50px}th:nth-child(3),td:nth-child(3){min-width:95px}th:nth-child(4),td:nth-child(4){min-width:60px}th:nth-child(5),td:nth-child(5){min-width:75px}th:nth-child(6),td:nth-child(6){min-width:70px}button{height:48px;font-size:15px;margin:6px 0}.button-container{padding:0 5px}}section{width:100%;display:flex;justify-content:center}\n/*$vite$:1*/",document.head.appendChild(e);const u={id:"app"},x={style:{"margin-bottom":"15px","font-weight":"bold","text-align":"center"}},b={class:"filter-item"},w=["value"],y=["value"],k=["value"],S={class:"table-container"},z={id:"attendanceTable"},F={key:0},D={key:0},$={key:0},v={key:0},K={key:0},T={style:{display:"none"}},C={class:"button-container"};t("default",f({name:"PantauAsrama",data:()=>({attendanceData:[],filters:{sesi:"",ranah:"",kelompok:"",nama:"",tanggal:"",acara:"",lokasi:""},apiKey:"",sortKey:"",sortOrder:"asc"}),computed:{uniqueSesi(){return[...new Set(this.attendanceData.map(t=>t.sesi))].sort()},uniqueRanah(){return[...new Set(this.attendanceData.map(t=>t.ranah))].sort()},uniqueKelompok(){const t=this.filters.ranah?this.attendanceData.filter(t=>t.ranah===this.filters.ranah):this.attendanceData;return[...new Set(t.map(t=>t.detail_ranah))].sort()},filteredData(){const t=this.attendanceData.filter(t=>{const e=!this.filters.sesi||t.sesi===this.filters.sesi,a=!this.filters.ranah||t.ranah===this.filters.ranah,i=!this.filters.kelompok||t.detail_ranah===this.filters.kelompok,n=!this.filters.nama||t.nama.toLowerCase().includes(this.filters.nama.toLowerCase());return e&&a&&i&&n});return this.sortKey&&t.sort((t,e)=>{let a="index"===this.sortKey?1:t[this.sortKey],i="index"===this.sortKey?1:e[this.sortKey];return"string"==typeof a&&(a=a.toLowerCase()),"string"==typeof i&&(i=i.toLowerCase()),a<i?"asc"===this.sortOrder?-1:1:a>i?"asc"===this.sortOrder?1:-1:0}),t}},methods:{async fetchDataForDate(){const t=`/api/absen-asramaan/?tanggal=${this.filters.tanggal}&acara=${this.filters.acara}&lokasi=${this.filters.lokasi||""}`;try{const e=await fetch(t,{headers:{Authorization:`ApiKey ${this.apiKey}`}});if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);this.attendanceData=await e.json()}catch(e){console.error("Error fetching data:",e)}},formatTanggal(t){const e=new Date(t);return`${["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"][e.getDay()]}, ${e.getDate()} ${["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"][e.getMonth()]} ${e.getFullYear()}`},async downloadPDF(){const t=new a({unit:"cm",format:"a4",margins:{top:1,bottom:1,left:1,right:1}});t.setFont("Times","normal"),t.setFontSize(20),t.text("Laporan Kehadiran Acara",t.internal.pageSize.getWidth()/2,2,{align:"center"}),t.setFontSize(16),t.text(`${this.filters.acara}`,t.internal.pageSize.getWidth()/2,2.8,{align:"center"}),t.setFontSize(20),t.text(`${this.filters.ranah||"Semua"}`,t.internal.pageSize.getWidth()/2,3.6,{align:"center"}),t.setFontSize(16);const e=this.formatTanggal(this.filters.tanggal);t.text(`${e}`,t.internal.pageSize.getWidth()/2,4.4,{align:"center"}),i(t,{head:[["No.","Sesi","Nama","Kelompok","Jam Hadir"]],body:this.filteredData.map((t,e)=>[e+1,t.sesi,t.nama,t.detail_ranah,t.jam_hadir]),startY:5.5,margin:{top:1,right:1,left:1,bottom:2},styles:{fontSize:10,cellPadding:.5},pageBreak:"auto",bodyStyles:{minCellHeight:.5},columnStyles:{0:{cellWidth:2},3:{cellWidth:5}},didDrawPage:e=>{const a=e.pageNumber,i=t.internal.pageSize.height,n=t.internal.pageSize.width;t.setFontSize(10);const r=i-1.5;t.setDrawColor(200,200,200),t.setLineWidth(.02),t.line(1,r,n-1,r);const l=`ABSENSI ${this.filters.acara} - ${this.filters.ranah} - ${this.filters.tanggal} - Halaman ${a}`;t.text(l,n-1,r+.5,{align:"right"})}});const n=`kehadiran-acara-${this.filters.acara||"UMUM"}-${this.filters.sesi||"semua"}-${this.filters.ranah||"semua"}-${this.filters.nama||"semua"}-${this.filters.tanggal||"semua"}.pdf`;t.save(n)},async downloadAllPDF(){const t=new a({unit:"cm",format:"a4",margins:{top:1,bottom:1,left:1,right:1}}),e=[...new Set(this.filteredData.map(t=>t.ranah))],n=(a=0)=>{if(a>=e.length)return void t.save("kehadiran-all.pdf");const r=e[a];a>0&&t.addPage(),t.setFont("Times","normal"),t.setFontSize(20),t.text("Laporan Kehadiran Acara",t.internal.pageSize.getWidth()/2,2,{align:"center"}),t.setFontSize(16),t.text(`${this.filters.acara||"UMUM"}`,t.internal.pageSize.getWidth()/2,2.8,{align:"center"}),t.setFontSize(20),t.text(`${r}`,t.internal.pageSize.getWidth()/2,3.6,{align:"center"}),t.setFontSize(16);const l=this.formatTanggal(this.filters.tanggal);t.text(`${l}`,t.internal.pageSize.getWidth()/2,4.4,{align:"center"});const o=this.filteredData.filter(t=>t.ranah===r);i(t,{head:[["No.","Sesi","Nama","Kelompok","Jam Hadir"]],body:o.map((t,e)=>[e+1,t.sesi,t.nama,t.detail_ranah,t.jam_hadir]),startY:5.5,margin:{top:1,right:1,left:1,bottom:2},styles:{fontSize:10,cellPadding:.5},pageBreak:"auto",bodyStyles:{minCellHeight:.5},columnStyles:{0:{cellWidth:2},3:{cellWidth:5}},didDrawPage:e=>{const a=t.internal.getCurrentPageInfo().pageNumber,i=t.internal.pageSize.height,n=t.internal.pageSize.width;t.setFontSize(10);const l=i-1.5;t.setDrawColor(200,200,200),t.setLineWidth(.02),t.line(1,l,n-1,l);const o=`ABSENSI ${this.filters.acara} - ${r} - ${this.filters.tanggal} - Halaman ${a}`;t.text(o,n-1,l+.5,{align:"right"})}}),n(a+1)};n()},sortTable(t){this.sortKey===t?this.sortOrder="asc"===this.sortOrder?"desc":"asc":(this.sortKey=t,this.sortOrder="asc")},openStatistics(){const t=`stat-ngaji.html?key=${this.apiKey}&acara=${this.filters.acara}&lokasi=${this.filters.lokasi}`;window.open(t,"_blank")},filterTable(){}},watch:{"filters.acara"(t){document.title=`Pantauan Kehadiran Acara - ${t||"UMUM"}`},"filters.ranah"(){this.filters.kelompok=""}},mounted(){const t=new Date;this.filters.tanggal=`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`;const e=new URLSearchParams(window.location.search);this.apiKey=e.get("key"),this.filters.acara=e.get("acara")||"",this.filters.lokasi=e.get("lokasi")||"",this.fetchDataForDate(),document.title=`Pantauan Kehadiran - ${this.filters.acara||"UMUM"}`}},[["render",function(t,e,a,i,f,P){return m(),n("div",u,[e[33]||(e[33]=r("h1",null,"Pantauan Kehadiran",-1)),r("div",x,l(f.filters.acara||"UMUM"),1),r("section",null,[r("div",b,[r("div",null,[e[19]||(e[19]=r("label",{for:"sesiFilter"},"Filter Sesi:",-1)),o(r("select",{"onUpdate:modelValue":e[0]||(e[0]=t=>f.filters.sesi=t),onChange:e[1]||(e[1]=(...t)=>P.filterTable&&P.filterTable(...t)),id:"sesiFilter"},[e[18]||(e[18]=r("option",{value:""},"Semua",-1)),(m(!0),n(d,null,h(P.uniqueSesi,t=>(m(),n("option",{key:t,value:t},l(t),9,w))),128))],544),[[s,f.filters.sesi]])]),r("div",null,[e[21]||(e[21]=r("label",{for:"desaFilter"},"Filter Desa:",-1)),o(r("select",{"onUpdate:modelValue":e[2]||(e[2]=t=>f.filters.ranah=t),onChange:e[3]||(e[3]=(...t)=>P.filterTable&&P.filterTable(...t)),id:"desaFilter"},[e[20]||(e[20]=r("option",{value:""},"Semua",-1)),(m(!0),n(d,null,h(P.uniqueRanah,t=>(m(),n("option",{key:t,value:t},l(t),9,y))),128))],544),[[s,f.filters.ranah]])]),r("div",null,[e[23]||(e[23]=r("label",{for:"kelompokFilter"},"Filter Kelompok:",-1)),o(r("select",{"onUpdate:modelValue":e[4]||(e[4]=t=>f.filters.kelompok=t),onChange:e[5]||(e[5]=(...t)=>P.filterTable&&P.filterTable(...t)),id:"kelompokFilter"},[e[22]||(e[22]=r("option",{value:""},"Semua",-1)),(m(!0),n(d,null,h(P.uniqueKelompok,t=>(m(),n("option",{key:t,value:t},l(t),9,k))),128))],544),[[s,f.filters.kelompok]])]),r("div",null,[e[24]||(e[24]=r("label",{for:"tanggalFilter"},"Filter Tanggal:",-1)),o(r("input",{type:"date","onUpdate:modelValue":e[6]||(e[6]=t=>f.filters.tanggal=t),onInput:e[7]||(e[7]=(...t)=>P.fetchDataForDate&&P.fetchDataForDate(...t)),id:"tanggalFilter"},null,544),[[p,f.filters.tanggal]])]),r("div",null,[e[25]||(e[25]=r("label",{for:"namaFilter"},"Filter Nama:",-1)),o(r("input",{type:"text","onUpdate:modelValue":e[8]||(e[8]=t=>f.filters.nama=t),onInput:e[9]||(e[9]=(...t)=>P.filterTable&&P.filterTable(...t)),placeholder:"Cari nama...",id:"namaFilter"},null,544),[[p,f.filters.nama]])])])]),r("div",S,[r("table",z,[r("thead",null,[r("tr",null,[e[31]||(e[31]=r("th",null,"No.",-1)),r("th",{onClick:e[10]||(e[10]=t=>P.sortTable("sesi"))},[e[26]||(e[26]=c(" Sesi ",-1)),"sesi"===f.sortKey?(m(),n("span",F,l("asc"===f.sortOrder?"↑":"↓"),1)):g("",!0)]),r("th",{onClick:e[11]||(e[11]=t=>P.sortTable("nama"))},[e[27]||(e[27]=c(" Nama ",-1)),"nama"===f.sortKey?(m(),n("span",D,l("asc"===f.sortOrder?"↑":"↓"),1)):g("",!0)]),r("th",{onClick:e[12]||(e[12]=t=>P.sortTable("ranah"))},[e[28]||(e[28]=c(" Desa ",-1)),"ranah"===f.sortKey?(m(),n("span",$,l("asc"===f.sortOrder?"↑":"↓"),1)):g("",!0)]),r("th",{onClick:e[13]||(e[13]=t=>P.sortTable("detail_ranah"))},[e[29]||(e[29]=c(" Kelompok ",-1)),"detail_ranah"===f.sortKey?(m(),n("span",v,l("asc"===f.sortOrder?"↑":"↓"),1)):g("",!0)]),r("th",{onClick:e[14]||(e[14]=t=>P.sortTable("jam_hadir"))},[e[30]||(e[30]=c(" Jam Hadir ",-1)),"jam_hadir"===f.sortKey?(m(),n("span",K,l("asc"===f.sortOrder?"↑":"↓"),1)):g("",!0)]),e[32]||(e[32]=r("th",{style:{display:"none"}},"Tanggal",-1))])]),r("tbody",null,[(m(!0),n(d,null,h(P.filteredData,(t,e)=>(m(),n("tr",{key:e},[r("td",null,l(e+1),1),r("td",null,l(t.sesi),1),r("td",null,l(t.nama),1),r("td",null,l(t.ranah),1),r("td",null,l(t.detail_ranah),1),r("td",null,l(t.jam_hadir),1),r("td",T,l(t.tanggal),1)]))),128))])])]),r("div",C,[r("button",{onClick:e[15]||(e[15]=(...t)=>P.downloadPDF&&P.downloadPDF(...t))},"Download as PDF"),r("button",{onClick:e[16]||(e[16]=(...t)=>P.openStatistics&&P.openStatistics(...t))},"View Statistics"),r("button",{onClick:e[17]||(e[17]=(...t)=>P.downloadAllPDF&&P.downloadAllPDF(...t))},"Download All (By Ranah)")])])}]]))}}});
