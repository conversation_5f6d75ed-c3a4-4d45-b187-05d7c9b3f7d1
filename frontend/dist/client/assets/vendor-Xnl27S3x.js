/**
* @vue/shared v3.5.18
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ls(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Y={},vt=[],De=()=>{},Qi=()=>!1,xn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),cs=e=>e.startsWith("onUpdate:"),ce=Object.assign,fs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Yi=Object.prototype.hasOwnProperty,W=(e,t)=>Yi.call(e,t),H=Array.isArray,bt=e=>Jt(e)==="[object Map]",wn=e=>Jt(e)==="[object Set]",Ms=e=>Jt(e)==="[object Date]",L=e=>typeof e=="function",te=e=>typeof e=="string",He=e=>typeof e=="symbol",Z=e=>e!==null&&typeof e=="object",Cr=e=>(Z(e)||L(e))&&L(e.then)&&L(e.catch),Pr=Object.prototype.toString,Jt=e=>Pr.call(e),Ji=e=>Jt(e).slice(8,-1),Ar=e=>Jt(e)==="[object Object]",us=e=>te(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,jt=ls(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Sn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Xi=/-(\w)/g,we=Sn(e=>e.replace(Xi,(t,n)=>n?n.toUpperCase():"")),Zi=/\B([A-Z])/g,ot=Sn(e=>e.replace(Zi,"-$1").toLowerCase()),En=Sn(e=>e.charAt(0).toUpperCase()+e.slice(1)),jn=Sn(e=>e?"on".concat(En(e)):""),st=(e,t)=>!Object.is(e,t),rn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Gn=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},an=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Is;const Rn=()=>Is||(Is=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function as(e){if(H(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=te(s)?so(s):as(s);if(r)for(const i in r)t[i]=r[i]}return t}else if(te(e)||Z(e))return e}const eo=/;(?![^(]*\))/g,to=/:([^]+)/,no=/\/\*[^]*?\*\//g;function so(e){const t={};return e.replace(no,"").split(eo).forEach(n=>{if(n){const s=n.split(to);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function hs(e){let t="";if(te(e))t=e;else if(H(e))for(let n=0;n<e.length;n++){const s=hs(e[n]);s&&(t+=s+" ")}else if(Z(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const ro="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",io=ls(ro);function Or(e){return!!e||e===""}function oo(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Cn(e[s],t[s]);return n}function Cn(e,t){if(e===t)return!0;let n=Ms(e),s=Ms(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=He(e),s=He(t),n||s)return e===t;if(n=H(e),s=H(t),n||s)return n&&s?oo(e,t):!1;if(n=Z(e),s=Z(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,i=Object.keys(t).length;if(r!==i)return!1;for(const o in e){const l=e.hasOwnProperty(o),c=t.hasOwnProperty(o);if(l&&!c||!l&&c||!Cn(e[o],t[o]))return!1}}return String(e)===String(t)}function lo(e,t){return e.findIndex(n=>Cn(n,t))}const Tr=e=>!!(e&&e.__v_isRef===!0),co=e=>te(e)?e:e==null?"":H(e)||Z(e)&&(e.toString===Pr||!L(e.toString))?Tr(e)?co(e.value):JSON.stringify(e,Mr,2):String(e),Mr=(e,t)=>Tr(t)?Mr(e,t.value):bt(t)?{["Map(".concat(t.size,")")]:[...t.entries()].reduce((n,[s,r],i)=>(n[Dn(s,i)+" =>"]=r,n),{})}:wn(t)?{["Set(".concat(t.size,")")]:[...t.values()].map(n=>Dn(n))}:He(t)?Dn(t):Z(t)&&!H(t)&&!Ar(t)?String(t):t,Dn=(e,t="")=>{var n;return He(e)?"Symbol(".concat((n=e.description)!=null?n:t,")"):e};/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let pe;class Ir{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=pe,!t&&pe&&(this.index=(pe.scopes||(pe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=pe;try{return pe=this,t()}finally{pe=n}}}on(){++this._on===1&&(this.prevScope=pe,pe=this)}off(){this._on>0&&--this._on===0&&(pe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function fo(e){return new Ir(e)}function uo(){return pe}let X;const Hn=new WeakSet;class Fr{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,pe&&pe.active&&pe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Hn.has(this)&&(Hn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||jr(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Fs(this),Dr(this);const t=X,n=Ee;X=this,Ee=!0;try{return this.fn()}finally{Hr(this),X=t,Ee=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)gs(t);this.deps=this.depsTail=void 0,Fs(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Hn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){zn(this)&&this.run()}get dirty(){return zn(this)}}let Nr=0,Dt,Ht;function jr(e,t=!1){if(e.flags|=8,t){e.next=Ht,Ht=e;return}e.next=Dt,Dt=e}function ds(){Nr++}function ps(){if(--Nr>0)return;if(Ht){let t=Ht;for(Ht=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Dt;){let t=Dt;for(Dt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Dr(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Hr(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),gs(s),ao(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function zn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&($r(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function $r(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===kt)||(e.globalVersion=kt,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!zn(e))))return;e.flags|=2;const t=e.dep,n=X,s=Ee;X=e,Ee=!0;try{Dr(e);const r=e.fn(e._value);(t.version===0||st(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{X=n,Ee=s,Hr(e),e.flags&=-3}}function gs(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)gs(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function ao(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ee=!0;const Lr=[];function Ge(){Lr.push(Ee),Ee=!1}function ze(){const e=Lr.pop();Ee=e===void 0?!0:e}function Fs(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=X;X=void 0;try{t()}finally{X=n}}}let kt=0;class ho{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ms{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!X||!Ee||X===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==X)n=this.activeLink=new ho(X,this),X.deps?(n.prevDep=X.depsTail,X.depsTail.nextDep=n,X.depsTail=n):X.deps=X.depsTail=n,Kr(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=X.depsTail,n.nextDep=void 0,X.depsTail.nextDep=n,X.depsTail=n,X.deps===n&&(X.deps=s)}return n}trigger(t){this.version++,kt++,this.notify(t)}notify(t){ds();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{ps()}}}function Kr(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Kr(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Qn=new WeakMap,ht=Symbol(""),Yn=Symbol(""),Wt=Symbol("");function ie(e,t,n){if(Ee&&X){let s=Qn.get(e);s||Qn.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new ms),r.map=s,r.key=n),r.track()}}function ke(e,t,n,s,r,i){const o=Qn.get(e);if(!o){kt++;return}const l=c=>{c&&c.trigger()};if(ds(),t==="clear")o.forEach(l);else{const c=H(e),d=c&&us(n);if(c&&n==="length"){const a=Number(s);o.forEach((h,g)=>{(g==="length"||g===Wt||!He(g)&&g>=a)&&l(h)})}else switch((n!==void 0||o.has(void 0))&&l(o.get(n)),d&&l(o.get(Wt)),t){case"add":c?d&&l(o.get("length")):(l(o.get(ht)),bt(e)&&l(o.get(Yn)));break;case"delete":c||(l(o.get(ht)),bt(e)&&l(o.get(Yn)));break;case"set":bt(e)&&l(o.get(ht));break}}ps()}function mt(e){const t=k(e);return t===e?t:(ie(t,"iterate",Wt),xe(e)?t:t.map(re))}function Pn(e){return ie(e=k(e),"iterate",Wt),e}const po={__proto__:null,[Symbol.iterator](){return $n(this,Symbol.iterator,re)},concat(...e){return mt(this).concat(...e.map(t=>H(t)?mt(t):t))},entries(){return $n(this,"entries",e=>(e[1]=re(e[1]),e))},every(e,t){return Ke(this,"every",e,t,void 0,arguments)},filter(e,t){return Ke(this,"filter",e,t,n=>n.map(re),arguments)},find(e,t){return Ke(this,"find",e,t,re,arguments)},findIndex(e,t){return Ke(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ke(this,"findLast",e,t,re,arguments)},findLastIndex(e,t){return Ke(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ke(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ln(this,"includes",e)},indexOf(...e){return Ln(this,"indexOf",e)},join(e){return mt(this).join(e)},lastIndexOf(...e){return Ln(this,"lastIndexOf",e)},map(e,t){return Ke(this,"map",e,t,void 0,arguments)},pop(){return Mt(this,"pop")},push(...e){return Mt(this,"push",e)},reduce(e,...t){return Ns(this,"reduce",e,t)},reduceRight(e,...t){return Ns(this,"reduceRight",e,t)},shift(){return Mt(this,"shift")},some(e,t){return Ke(this,"some",e,t,void 0,arguments)},splice(...e){return Mt(this,"splice",e)},toReversed(){return mt(this).toReversed()},toSorted(e){return mt(this).toSorted(e)},toSpliced(...e){return mt(this).toSpliced(...e)},unshift(...e){return Mt(this,"unshift",e)},values(){return $n(this,"values",re)}};function $n(e,t,n){const s=Pn(e),r=s[t]();return s!==e&&!xe(e)&&(r._next=r.next,r.next=()=>{const i=r._next();return i.value&&(i.value=n(i.value)),i}),r}const go=Array.prototype;function Ke(e,t,n,s,r,i){const o=Pn(e),l=o!==e&&!xe(e),c=o[t];if(c!==go[t]){const h=c.apply(e,i);return l?re(h):h}let d=n;o!==e&&(l?d=function(h,g){return n.call(this,re(h),g,e)}:n.length>2&&(d=function(h,g){return n.call(this,h,g,e)}));const a=c.call(o,d,s);return l&&r?r(a):a}function Ns(e,t,n,s){const r=Pn(e);let i=n;return r!==e&&(xe(e)?n.length>3&&(i=function(o,l,c){return n.call(this,o,l,c,e)}):i=function(o,l,c){return n.call(this,o,re(l),c,e)}),r[t](i,...s)}function Ln(e,t,n){const s=k(e);ie(s,"iterate",Wt);const r=s[t](...n);return(r===-1||r===!1)&&vs(n[0])?(n[0]=k(n[0]),s[t](...n)):r}function Mt(e,t,n=[]){Ge(),ds();const s=k(e)[t].apply(e,n);return ps(),ze(),s}const mo=ls("__proto__,__v_isRef,__isVue"),Vr=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(He));function _o(e){He(e)||(e=String(e));const t=k(this);return ie(t,"has",e),t.hasOwnProperty(e)}class Br{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return i;if(n==="__v_raw")return s===(r?i?Po:qr:i?Wr:kr).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const o=H(t);if(!r){let c;if(o&&(c=po[n]))return c;if(n==="hasOwnProperty")return _o}const l=Reflect.get(t,n,le(t)?t:s);return(He(n)?Vr.has(n):mo(n))||(r||ie(t,"get",n),i)?l:le(l)?o&&us(n)?l:l.value:Z(l)?r?zr(l):An(l):l}}class Ur extends Br{constructor(t=!1){super(!1,t)}set(t,n,s,r){let i=t[n];if(!this._isShallow){const c=rt(i);if(!xe(s)&&!rt(s)&&(i=k(i),s=k(s)),!H(t)&&le(i)&&!le(s))return c?!1:(i.value=s,!0)}const o=H(t)&&us(n)?Number(n)<t.length:W(t,n),l=Reflect.set(t,n,s,le(t)?t:r);return t===k(r)&&(o?st(s,i)&&ke(t,"set",n,s):ke(t,"add",n,s)),l}deleteProperty(t,n){const s=W(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&ke(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!He(n)||!Vr.has(n))&&ie(t,"has",n),s}ownKeys(t){return ie(t,"iterate",H(t)?"length":ht),Reflect.ownKeys(t)}}class yo extends Br{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const vo=new Ur,bo=new yo,xo=new Ur(!0);const Jn=e=>e,tn=e=>Reflect.getPrototypeOf(e);function wo(e,t,n){return function(...s){const r=this.__v_raw,i=k(r),o=bt(i),l=e==="entries"||e===Symbol.iterator&&o,c=e==="keys"&&o,d=r[e](...s),a=n?Jn:t?hn:re;return!t&&ie(i,"iterate",c?Yn:ht),{next(){const{value:h,done:g}=d.next();return g?{value:h,done:g}:{value:l?[a(h[0]),a(h[1])]:a(h),done:g}},[Symbol.iterator](){return this}}}}function nn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function So(e,t){const n={get(r){const i=this.__v_raw,o=k(i),l=k(r);e||(st(r,l)&&ie(o,"get",r),ie(o,"get",l));const{has:c}=tn(o),d=t?Jn:e?hn:re;if(c.call(o,r))return d(i.get(r));if(c.call(o,l))return d(i.get(l));i!==o&&i.get(r)},get size(){const r=this.__v_raw;return!e&&ie(k(r),"iterate",ht),Reflect.get(r,"size",r)},has(r){const i=this.__v_raw,o=k(i),l=k(r);return e||(st(r,l)&&ie(o,"has",r),ie(o,"has",l)),r===l?i.has(r):i.has(r)||i.has(l)},forEach(r,i){const o=this,l=o.__v_raw,c=k(l),d=t?Jn:e?hn:re;return!e&&ie(c,"iterate",ht),l.forEach((a,h)=>r.call(i,d(a),d(h),o))}};return ce(n,e?{add:nn("add"),set:nn("set"),delete:nn("delete"),clear:nn("clear")}:{add(r){!t&&!xe(r)&&!rt(r)&&(r=k(r));const i=k(this);return tn(i).has.call(i,r)||(i.add(r),ke(i,"add",r,r)),this},set(r,i){!t&&!xe(i)&&!rt(i)&&(i=k(i));const o=k(this),{has:l,get:c}=tn(o);let d=l.call(o,r);d||(r=k(r),d=l.call(o,r));const a=c.call(o,r);return o.set(r,i),d?st(i,a)&&ke(o,"set",r,i):ke(o,"add",r,i),this},delete(r){const i=k(this),{has:o,get:l}=tn(i);let c=o.call(i,r);c||(r=k(r),c=o.call(i,r)),l&&l.call(i,r);const d=i.delete(r);return c&&ke(i,"delete",r,void 0),d},clear(){const r=k(this),i=r.size!==0,o=r.clear();return i&&ke(r,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=wo(r,e,t)}),n}function _s(e,t){const n=So(e,t);return(s,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(W(n,r)&&r in s?n:s,r,i)}const Eo={get:_s(!1,!1)},Ro={get:_s(!1,!0)},Co={get:_s(!0,!1)};const kr=new WeakMap,Wr=new WeakMap,qr=new WeakMap,Po=new WeakMap;function Ao(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Oo(e){return e.__v_skip||!Object.isExtensible(e)?0:Ao(Ji(e))}function An(e){return rt(e)?e:ys(e,!1,vo,Eo,kr)}function Gr(e){return ys(e,!1,xo,Ro,Wr)}function zr(e){return ys(e,!0,bo,Co,qr)}function ys(e,t,n,s,r){if(!Z(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=Oo(e);if(i===0)return e;const o=r.get(e);if(o)return o;const l=new Proxy(e,i===2?s:n);return r.set(e,l),l}function xt(e){return rt(e)?xt(e.__v_raw):!!(e&&e.__v_isReactive)}function rt(e){return!!(e&&e.__v_isReadonly)}function xe(e){return!!(e&&e.__v_isShallow)}function vs(e){return e?!!e.__v_raw:!1}function k(e){const t=e&&e.__v_raw;return t?k(t):e}function Qr(e){return!W(e,"__v_skip")&&Object.isExtensible(e)&&Gn(e,"__v_skip",!0),e}const re=e=>Z(e)?An(e):e,hn=e=>Z(e)?zr(e):e;function le(e){return e?e.__v_isRef===!0:!1}function Yr(e){return Jr(e,!1)}function To(e){return Jr(e,!0)}function Jr(e,t){return le(e)?e:new Mo(e,t)}class Mo{constructor(t,n){this.dep=new ms,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:k(t),this._value=n?t:re(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||xe(t)||rt(t);t=s?t:k(t),st(t,n)&&(this._rawValue=t,this._value=s?t:re(t),this.dep.trigger())}}function wt(e){return le(e)?e.value:e}const Io={get:(e,t,n)=>t==="__v_raw"?e:wt(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return le(r)&&!le(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function Xr(e){return xt(e)?e:new Proxy(e,Io)}class Fo{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new ms(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=kt-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&X!==this)return jr(this,!0),!0}get value(){const t=this.dep.track();return $r(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function No(e,t,n=!1){let s,r;return L(e)?s=e:(s=e.get,r=e.set),new Fo(s,r,n)}const sn={},dn=new WeakMap;let ut;function jo(e,t=!1,n=ut){if(n){let s=dn.get(n);s||dn.set(n,s=[]),s.push(e)}}function Do(e,t,n=Y){const{immediate:s,deep:r,once:i,scheduler:o,augmentJob:l,call:c}=n,d=T=>r?T:xe(T)||r===!1||r===0?We(T,1):We(T);let a,h,g,m,A=!1,O=!1;if(le(e)?(h=()=>e.value,A=xe(e)):xt(e)?(h=()=>d(e),A=!0):H(e)?(O=!0,A=e.some(T=>xt(T)||xe(T)),h=()=>e.map(T=>{if(le(T))return T.value;if(xt(T))return d(T);if(L(T))return c?c(T,2):T()})):L(e)?t?h=c?()=>c(e,2):e:h=()=>{if(g){Ge();try{g()}finally{ze()}}const T=ut;ut=a;try{return c?c(e,3,[m]):e(m)}finally{ut=T}}:h=De,t&&r){const T=h,z=r===!0?1/0:r;h=()=>We(T(),z)}const K=uo(),j=()=>{a.stop(),K&&K.active&&fs(K.effects,a)};if(i&&t){const T=t;t=(...z)=>{T(...z),j()}}let I=O?new Array(e.length).fill(sn):sn;const D=T=>{if(!(!(a.flags&1)||!a.dirty&&!T))if(t){const z=a.run();if(r||A||(O?z.some((se,ee)=>st(se,I[ee])):st(z,I))){g&&g();const se=ut;ut=a;try{const ee=[z,I===sn?void 0:O&&I[0]===sn?[]:I,m];I=z,c?c(t,3,ee):t(...ee)}finally{ut=se}}}else a.run()};return l&&l(D),a=new Fr(h),a.scheduler=o?()=>o(D,!1):D,m=T=>jo(T,!1,a),g=a.onStop=()=>{const T=dn.get(a);if(T){if(c)c(T,4);else for(const z of T)z();dn.delete(a)}},t?s?D(!0):I=a.run():o?o(D.bind(null,!0),!0):a.run(),j.pause=a.pause.bind(a),j.resume=a.resume.bind(a),j.stop=j,j}function We(e,t=1/0,n){if(t<=0||!Z(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,le(e))We(e.value,t,n);else if(H(e))for(let s=0;s<e.length;s++)We(e[s],t,n);else if(wn(e)||bt(e))e.forEach(s=>{We(s,t,n)});else if(Ar(e)){for(const s in e)We(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&We(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Xt(e,t,n,s){try{return s?e(...s):e()}catch(r){On(r,t,n)}}function $e(e,t,n,s){if(L(e)){const r=Xt(e,t,n,s);return r&&Cr(r)&&r.catch(i=>{On(i,t,n)}),r}if(H(e)){const r=[];for(let i=0;i<e.length;i++)r.push($e(e[i],t,n,s));return r}}function On(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||Y;if(t){let l=t.parent;const c=t.proxy,d="https://vuejs.org/error-reference/#runtime-".concat(n);for(;l;){const a=l.ec;if(a){for(let h=0;h<a.length;h++)if(a[h](e,c,d)===!1)return}l=l.parent}if(i){Ge(),Xt(i,null,10,[e,c,d]),ze();return}}Ho(e,n,r,s,o)}function Ho(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const ue=[];let Ne=-1;const St=[];let et=null,_t=0;const Zr=Promise.resolve();let pn=null;function bs(e){const t=pn||Zr;return e?t.then(this?e.bind(this):e):t}function $o(e){let t=Ne+1,n=ue.length;for(;t<n;){const s=t+n>>>1,r=ue[s],i=qt(r);i<e||i===e&&r.flags&2?t=s+1:n=s}return t}function xs(e){if(!(e.flags&1)){const t=qt(e),n=ue[ue.length-1];!n||!(e.flags&2)&&t>=qt(n)?ue.push(e):ue.splice($o(t),0,e),e.flags|=1,ei()}}function ei(){pn||(pn=Zr.then(ni))}function Lo(e){H(e)?St.push(...e):et&&e.id===-1?et.splice(_t+1,0,e):e.flags&1||(St.push(e),e.flags|=1),ei()}function js(e,t,n=Ne+1){for(;n<ue.length;n++){const s=ue[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;ue.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function ti(e){if(St.length){const t=[...new Set(St)].sort((n,s)=>qt(n)-qt(s));if(St.length=0,et){et.push(...t);return}for(et=t,_t=0;_t<et.length;_t++){const n=et[_t];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}et=null,_t=0}}const qt=e=>e.id==null?e.flags&2?-1:1/0:e.id;function ni(e){try{for(Ne=0;Ne<ue.length;Ne++){const t=ue[Ne];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Xt(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Ne<ue.length;Ne++){const t=ue[Ne];t&&(t.flags&=-2)}Ne=-1,ue.length=0,ti(),pn=null,(ue.length||St.length)&&ni()}}let ye=null,si=null;function gn(e){const t=ye;return ye=e,si=e&&e.type.__scopeId||null,t}function Ko(e,t=ye,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Ws(-1);const i=gn(t);let o;try{o=e(...r)}finally{gn(i),s._d&&Ws(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function Sf(e,t){if(ye===null)return e;const n=Fn(ye),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[i,o,l,c=Y]=t[r];i&&(L(i)&&(i={mounted:i,updated:i}),i.deep&&We(o),s.push({dir:i,instance:n,value:o,oldValue:void 0,arg:l,modifiers:c}))}return e}function ct(e,t,n,s){const r=e.dirs,i=t&&t.dirs;for(let o=0;o<r.length;o++){const l=r[o];i&&(l.oldValue=i[o].value);let c=l.dir[s];c&&(Ge(),$e(c,n,8,[e.el,l,e,t]),ze())}}const Vo=Symbol("_vte"),Bo=e=>e.__isTeleport;function ws(e,t){e.shapeFlag&6&&e.component?(e.transition=t,ws(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function ri(e,t){return L(e)?ce({name:e.name},t,{setup:e}):e}function ii(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function $t(e,t,n,s,r=!1){if(H(e)){e.forEach((A,O)=>$t(A,t&&(H(t)?t[O]:t),n,s,r));return}if(Lt(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&$t(e,t,n,s.component.subTree);return}const i=s.shapeFlag&4?Fn(s.component):s.el,o=r?null:i,{i:l,r:c}=e,d=t&&t.r,a=l.refs===Y?l.refs={}:l.refs,h=l.setupState,g=k(h),m=h===Y?()=>!1:A=>W(g,A);if(d!=null&&d!==c&&(te(d)?(a[d]=null,m(d)&&(h[d]=null)):le(d)&&(d.value=null)),L(c))Xt(c,l,12,[o,a]);else{const A=te(c),O=le(c);if(A||O){const K=()=>{if(e.f){const j=A?m(c)?h[c]:a[c]:c.value;r?H(j)&&fs(j,i):H(j)?j.includes(i)||j.push(i):A?(a[c]=[i],m(c)&&(h[c]=a[c])):(c.value=[i],e.k&&(a[e.k]=c.value))}else A?(a[c]=o,m(c)&&(h[c]=o)):O&&(c.value=o,e.k&&(a[e.k]=o))};o?(K.id=-1,_e(K,n)):K()}}}Rn().requestIdleCallback;Rn().cancelIdleCallback;const Lt=e=>!!e.type.__asyncLoader,oi=e=>e.type.__isKeepAlive;function Uo(e,t){li(e,"a",t)}function ko(e,t){li(e,"da",t)}function li(e,t,n=oe){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Tn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)oi(r.parent.vnode)&&Wo(s,t,n,r),r=r.parent}}function Wo(e,t,n,s){const r=Tn(t,e,s,!0);ci(()=>{fs(s[t],r)},n)}function Tn(e,t,n=oe,s=!1){if(n){const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{Ge();const l=Zt(n),c=$e(t,n,e,o);return l(),ze(),c});return s?r.unshift(i):r.push(i),i}}const Qe=e=>(t,n=oe)=>{(!zt||e==="sp")&&Tn(e,(...s)=>t(...s),n)},qo=Qe("bm"),Go=Qe("m"),zo=Qe("bu"),Qo=Qe("u"),Yo=Qe("bum"),ci=Qe("um"),Jo=Qe("sp"),Xo=Qe("rtg"),Zo=Qe("rtc");function el(e,t=oe){Tn("ec",e,t)}const fi="components";function Ef(e,t){return ai(fi,e,!0,t)||e}const ui=Symbol.for("v-ndc");function Rf(e){return te(e)?ai(fi,e,!1)||e:e||ui}function ai(e,t,n=!0,s=!1){const r=ye||oe;if(r){const i=r.type;{const l=kl(i,!1);if(l&&(l===t||l===we(t)||l===En(we(t))))return i}const o=Ds(r[e]||i[e],t)||Ds(r.appContext[e],t);return!o&&s?i:o}}function Ds(e,t){return e&&(e[t]||e[we(t)]||e[En(we(t))])}function Cf(e,t,n,s){let r;const i=n,o=H(e);if(o||te(e)){const l=o&&xt(e);let c=!1,d=!1;l&&(c=!xe(e),d=rt(e),e=Pn(e)),r=new Array(e.length);for(let a=0,h=e.length;a<h;a++)r[a]=t(c?d?hn(re(e[a])):re(e[a]):e[a],a,void 0,i)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,i)}else if(Z(e))if(e[Symbol.iterator])r=Array.from(e,(l,c)=>t(l,c,void 0,i));else{const l=Object.keys(e);r=new Array(l.length);for(let c=0,d=l.length;c<d;c++){const a=l[c];r[c]=t(e[a],a,c,i)}}else r=[];return r}const Xn=e=>e?Ii(e)?Fn(e):Xn(e.parent):null,Kt=ce(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Xn(e.parent),$root:e=>Xn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>di(e),$forceUpdate:e=>e.f||(e.f=()=>{xs(e.update)}),$nextTick:e=>e.n||(e.n=bs.bind(e.proxy)),$watch:e=>xl.bind(e)}),Kn=(e,t)=>e!==Y&&!e.__isScriptSetup&&W(e,t),tl={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:i,accessCache:o,type:l,appContext:c}=e;let d;if(t[0]!=="$"){const m=o[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(Kn(s,t))return o[t]=1,s[t];if(r!==Y&&W(r,t))return o[t]=2,r[t];if((d=e.propsOptions[0])&&W(d,t))return o[t]=3,i[t];if(n!==Y&&W(n,t))return o[t]=4,n[t];Zn&&(o[t]=0)}}const a=Kt[t];let h,g;if(a)return t==="$attrs"&&ie(e.attrs,"get",""),a(e);if((h=l.__cssModules)&&(h=h[t]))return h;if(n!==Y&&W(n,t))return o[t]=4,n[t];if(g=c.config.globalProperties,W(g,t))return g[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:i}=e;return Kn(r,t)?(r[t]=n,!0):s!==Y&&W(s,t)?(s[t]=n,!0):W(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:i}},o){let l;return!!n[o]||e!==Y&&W(e,o)||Kn(t,o)||(l=i[0])&&W(l,o)||W(s,o)||W(Kt,o)||W(r.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:W(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Hs(e){return H(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Zn=!0;function nl(e){const t=di(e),n=e.proxy,s=e.ctx;Zn=!1,t.beforeCreate&&$s(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:o,watch:l,provide:c,inject:d,created:a,beforeMount:h,mounted:g,beforeUpdate:m,updated:A,activated:O,deactivated:K,beforeDestroy:j,beforeUnmount:I,destroyed:D,unmounted:T,render:z,renderTracked:se,renderTriggered:ee,errorCaptured:Ce,serverPrefetch:Ye,expose:Pe,inheritAttrs:Je,components:lt,directives:Ae,filters:Ot}=t;if(d&&sl(d,s,null),o)for(const G in o){const B=o[G];L(B)&&(s[G]=B.bind(n))}if(r){const G=r.call(n,n);Z(G)&&(e.data=An(G))}if(Zn=!0,i)for(const G in i){const B=i[G],Le=L(B)?B.bind(n,n):L(B.get)?B.get.bind(n,n):De,Xe=!L(B)&&L(B.set)?B.set.bind(n):De,Oe=Se({get:Le,set:Xe});Object.defineProperty(s,G,{enumerable:!0,configurable:!0,get:()=>Oe.value,set:ae=>Oe.value=ae})}if(l)for(const G in l)hi(l[G],s,n,G);if(c){const G=L(c)?c.call(n):c;Reflect.ownKeys(G).forEach(B=>{on(B,G[B])})}a&&$s(a,e,"c");function ne(G,B){H(B)?B.forEach(Le=>G(Le.bind(n))):B&&G(B.bind(n))}if(ne(qo,h),ne(Go,g),ne(zo,m),ne(Qo,A),ne(Uo,O),ne(ko,K),ne(el,Ce),ne(Zo,se),ne(Xo,ee),ne(Yo,I),ne(ci,T),ne(Jo,Ye),H(Pe))if(Pe.length){const G=e.exposed||(e.exposed={});Pe.forEach(B=>{Object.defineProperty(G,B,{get:()=>n[B],set:Le=>n[B]=Le,enumerable:!0})})}else e.exposed||(e.exposed={});z&&e.render===De&&(e.render=z),Je!=null&&(e.inheritAttrs=Je),lt&&(e.components=lt),Ae&&(e.directives=Ae),Ye&&ii(e)}function sl(e,t,n=De){H(e)&&(e=es(e));for(const s in e){const r=e[s];let i;Z(r)?"default"in r?i=qe(r.from||s,r.default,!0):i=qe(r.from||s):i=qe(r),le(i)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[s]=i}}function $s(e,t,n){$e(H(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function hi(e,t,n,s){let r=s.includes(".")?Ci(n,s):()=>n[s];if(te(e)){const i=t[e];L(i)&&ln(r,i)}else if(L(e))ln(r,e.bind(n));else if(Z(e))if(H(e))e.forEach(i=>hi(i,t,n,s));else{const i=L(e.handler)?e.handler.bind(n):t[e.handler];L(i)&&ln(r,i,e)}}function di(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,l=i.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(d=>mn(c,d,o,!0)),mn(c,t,o)),Z(t)&&i.set(t,c),c}function mn(e,t,n,s=!1){const{mixins:r,extends:i}=t;i&&mn(e,i,n,!0),r&&r.forEach(o=>mn(e,o,n,!0));for(const o in t)if(!(s&&o==="expose")){const l=rl[o]||n&&n[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const rl={data:Ls,props:Ks,emits:Ks,methods:Nt,computed:Nt,beforeCreate:fe,created:fe,beforeMount:fe,mounted:fe,beforeUpdate:fe,updated:fe,beforeDestroy:fe,beforeUnmount:fe,destroyed:fe,unmounted:fe,activated:fe,deactivated:fe,errorCaptured:fe,serverPrefetch:fe,components:Nt,directives:Nt,watch:ol,provide:Ls,inject:il};function Ls(e,t){return t?e?function(){return ce(L(e)?e.call(this,this):e,L(t)?t.call(this,this):t)}:t:e}function il(e,t){return Nt(es(e),es(t))}function es(e){if(H(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function fe(e,t){return e?[...new Set([].concat(e,t))]:t}function Nt(e,t){return e?ce(Object.create(null),e,t):t}function Ks(e,t){return e?H(e)&&H(t)?[...new Set([...e,...t])]:ce(Object.create(null),Hs(e),Hs(t!=null?t:{})):t}function ol(e,t){if(!e)return t;if(!t)return e;const n=ce(Object.create(null),e);for(const s in t)n[s]=fe(e[s],t[s]);return n}function pi(){return{app:null,config:{isNativeTag:Qi,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ll=0;function cl(e,t){return function(s,r=null){L(s)||(s=ce({},s)),r!=null&&!Z(r)&&(r=null);const i=pi(),o=new WeakSet,l=[];let c=!1;const d=i.app={_uid:ll++,_component:s,_props:r,_container:null,_context:i,_instance:null,version:ql,get config(){return i.config},set config(a){},use(a,...h){return o.has(a)||(a&&L(a.install)?(o.add(a),a.install(d,...h)):L(a)&&(o.add(a),a(d,...h))),d},mixin(a){return i.mixins.includes(a)||i.mixins.push(a),d},component(a,h){return h?(i.components[a]=h,d):i.components[a]},directive(a,h){return h?(i.directives[a]=h,d):i.directives[a]},mount(a,h,g){if(!c){const m=d._ceVNode||ge(s,r);return m.appContext=i,g===!0?g="svg":g===!1&&(g=void 0),e(m,a,g),c=!0,d._container=a,a.__vue_app__=d,Fn(m.component)}},onUnmount(a){l.push(a)},unmount(){c&&($e(l,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(a,h){return i.provides[a]=h,d},runWithContext(a){const h=Et;Et=d;try{return a()}finally{Et=h}}};return d}}let Et=null;function on(e,t){if(oe){let n=oe.provides;const s=oe.parent&&oe.parent.provides;s===n&&(n=oe.provides=Object.create(s)),n[e]=t}}function qe(e,t,n=!1){const s=Ll();if(s||Et){let r=Et?Et._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&L(t)?t.call(s&&s.proxy):t}}const gi={},mi=()=>Object.create(gi),_i=e=>Object.getPrototypeOf(e)===gi;function fl(e,t,n,s=!1){const r={},i=mi();e.propsDefaults=Object.create(null),yi(e,t,r,i);for(const o in e.propsOptions[0])o in r||(r[o]=void 0);n?e.props=s?r:Gr(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function ul(e,t,n,s){const{props:r,attrs:i,vnode:{patchFlag:o}}=e,l=k(r),[c]=e.propsOptions;let d=!1;if((s||o>0)&&!(o&16)){if(o&8){const a=e.vnode.dynamicProps;for(let h=0;h<a.length;h++){let g=a[h];if(Mn(e.emitsOptions,g))continue;const m=t[g];if(c)if(W(i,g))m!==i[g]&&(i[g]=m,d=!0);else{const A=we(g);r[A]=ts(c,l,A,m,e,!1)}else m!==i[g]&&(i[g]=m,d=!0)}}}else{yi(e,t,r,i)&&(d=!0);let a;for(const h in l)(!t||!W(t,h)&&((a=ot(h))===h||!W(t,a)))&&(c?n&&(n[h]!==void 0||n[a]!==void 0)&&(r[h]=ts(c,l,h,void 0,e,!0)):delete r[h]);if(i!==l)for(const h in i)(!t||!W(t,h))&&(delete i[h],d=!0)}d&&ke(e.attrs,"set","")}function yi(e,t,n,s){const[r,i]=e.propsOptions;let o=!1,l;if(t)for(let c in t){if(jt(c))continue;const d=t[c];let a;r&&W(r,a=we(c))?!i||!i.includes(a)?n[a]=d:(l||(l={}))[a]=d:Mn(e.emitsOptions,c)||(!(c in s)||d!==s[c])&&(s[c]=d,o=!0)}if(i){const c=k(n),d=l||Y;for(let a=0;a<i.length;a++){const h=i[a];n[h]=ts(r,c,h,d[h],e,!W(d,h))}}return o}function ts(e,t,n,s,r,i){const o=e[n];if(o!=null){const l=W(o,"default");if(l&&s===void 0){const c=o.default;if(o.type!==Function&&!o.skipFactory&&L(c)){const{propsDefaults:d}=r;if(n in d)s=d[n];else{const a=Zt(r);s=d[n]=c.call(null,t),a()}}else s=c;r.ce&&r.ce._setProp(n,s)}o[0]&&(i&&!l?s=!1:o[1]&&(s===""||s===ot(n))&&(s=!0))}return s}const al=new WeakMap;function vi(e,t,n=!1){const s=n?al:t.propsCache,r=s.get(e);if(r)return r;const i=e.props,o={},l=[];let c=!1;if(!L(e)){const a=h=>{c=!0;const[g,m]=vi(h,t,!0);ce(o,g),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!i&&!c)return Z(e)&&s.set(e,vt),vt;if(H(i))for(let a=0;a<i.length;a++){const h=we(i[a]);Vs(h)&&(o[h]=Y)}else if(i)for(const a in i){const h=we(a);if(Vs(h)){const g=i[a],m=o[h]=H(g)||L(g)?{type:g}:ce({},g),A=m.type;let O=!1,K=!0;if(H(A))for(let j=0;j<A.length;++j){const I=A[j],D=L(I)&&I.name;if(D==="Boolean"){O=!0;break}else D==="String"&&(K=!1)}else O=L(A)&&A.name==="Boolean";m[0]=O,m[1]=K,(O||W(m,"default"))&&l.push(h)}}const d=[o,l];return Z(e)&&s.set(e,d),d}function Vs(e){return e[0]!=="$"&&!jt(e)}const Ss=e=>e==="_"||e==="__"||e==="_ctx"||e==="$stable",Es=e=>H(e)?e.map(je):[je(e)],hl=(e,t,n)=>{if(t._n)return t;const s=Ko((...r)=>Es(t(...r)),n);return s._c=!1,s},bi=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Ss(r))continue;const i=e[r];if(L(i))t[r]=hl(r,i,s);else if(i!=null){const o=Es(i);t[r]=()=>o}}},xi=(e,t)=>{const n=Es(t);e.slots.default=()=>n},wi=(e,t,n)=>{for(const s in t)(n||!Ss(s))&&(e[s]=t[s])},dl=(e,t,n)=>{const s=e.slots=mi();if(e.vnode.shapeFlag&32){const r=t.__;r&&Gn(s,"__",r,!0);const i=t._;i?(wi(s,t,n),n&&Gn(s,"_",i,!0)):bi(t,s)}else t&&xi(e,t)},pl=(e,t,n)=>{const{vnode:s,slots:r}=e;let i=!0,o=Y;if(s.shapeFlag&32){const l=t._;l?n&&l===1?i=!1:wi(r,t,n):(i=!t.$stable,bi(t,r)),o=t}else t&&(xi(e,t),o={default:1});if(i)for(const l in r)!Ss(l)&&o[l]==null&&delete r[l]},_e=Al;function gl(e){return ml(e)}function ml(e,t){const n=Rn();n.__VUE__=!0;const{insert:s,remove:r,patchProp:i,createElement:o,createText:l,createComment:c,setText:d,setElementText:a,parentNode:h,nextSibling:g,setScopeId:m=De,insertStaticContent:A}=e,O=(f,u,p,_=null,b=null,v=null,E=void 0,S=null,w=!!u.dynamicChildren)=>{if(f===u)return;f&&!It(f,u)&&(_=y(f),ae(f,b,v,!0),f=null),u.patchFlag===-2&&(w=!1,u.dynamicChildren=null);const{type:x,ref:N,shapeFlag:C}=u;switch(x){case In:K(f,u,p,_);break;case it:j(f,u,p,_);break;case cn:f==null&&I(u,p,_,E);break;case Ue:lt(f,u,p,_,b,v,E,S,w);break;default:C&1?z(f,u,p,_,b,v,E,S,w):C&6?Ae(f,u,p,_,b,v,E,S,w):(C&64||C&128)&&x.process(f,u,p,_,b,v,E,S,w,M)}N!=null&&b?$t(N,f&&f.ref,v,u||f,!u):N==null&&f&&f.ref!=null&&$t(f.ref,null,v,f,!0)},K=(f,u,p,_)=>{if(f==null)s(u.el=l(u.children),p,_);else{const b=u.el=f.el;u.children!==f.children&&d(b,u.children)}},j=(f,u,p,_)=>{f==null?s(u.el=c(u.children||""),p,_):u.el=f.el},I=(f,u,p,_)=>{[f.el,f.anchor]=A(f.children,u,p,_,f.el,f.anchor)},D=({el:f,anchor:u},p,_)=>{let b;for(;f&&f!==u;)b=g(f),s(f,p,_),f=b;s(u,p,_)},T=({el:f,anchor:u})=>{let p;for(;f&&f!==u;)p=g(f),r(f),f=p;r(u)},z=(f,u,p,_,b,v,E,S,w)=>{u.type==="svg"?E="svg":u.type==="math"&&(E="mathml"),f==null?se(u,p,_,b,v,E,S,w):Ye(f,u,b,v,E,S,w)},se=(f,u,p,_,b,v,E,S)=>{let w,x;const{props:N,shapeFlag:C,transition:F,dirs:$}=f;if(w=f.el=o(f.type,v,N&&N.is,N),C&8?a(w,f.children):C&16&&Ce(f.children,w,null,_,b,Vn(f,v),E,S),$&&ct(f,null,_,"created"),ee(w,f,f.scopeId,E,_),N){for(const J in N)J!=="value"&&!jt(J)&&i(w,J,null,N[J],v,_);"value"in N&&i(w,"value",null,N.value,v),(x=N.onVnodeBeforeMount)&&Fe(x,_,f)}$&&ct(f,null,_,"beforeMount");const V=_l(b,F);V&&F.beforeEnter(w),s(w,u,p),((x=N&&N.onVnodeMounted)||V||$)&&_e(()=>{x&&Fe(x,_,f),V&&F.enter(w),$&&ct(f,null,_,"mounted")},b)},ee=(f,u,p,_,b)=>{if(p&&m(f,p),_)for(let v=0;v<_.length;v++)m(f,_[v]);if(b){let v=b.subTree;if(u===v||Ai(v.type)&&(v.ssContent===u||v.ssFallback===u)){const E=b.vnode;ee(f,E,E.scopeId,E.slotScopeIds,b.parent)}}},Ce=(f,u,p,_,b,v,E,S,w=0)=>{for(let x=w;x<f.length;x++){const N=f[x]=S?tt(f[x]):je(f[x]);O(null,N,u,p,_,b,v,E,S)}},Ye=(f,u,p,_,b,v,E)=>{const S=u.el=f.el;let{patchFlag:w,dynamicChildren:x,dirs:N}=u;w|=f.patchFlag&16;const C=f.props||Y,F=u.props||Y;let $;if(p&&ft(p,!1),($=F.onVnodeBeforeUpdate)&&Fe($,p,u,f),N&&ct(u,f,p,"beforeUpdate"),p&&ft(p,!0),(C.innerHTML&&F.innerHTML==null||C.textContent&&F.textContent==null)&&a(S,""),x?Pe(f.dynamicChildren,x,S,p,_,Vn(u,b),v):E||B(f,u,S,null,p,_,Vn(u,b),v,!1),w>0){if(w&16)Je(S,C,F,p,b);else if(w&2&&C.class!==F.class&&i(S,"class",null,F.class,b),w&4&&i(S,"style",C.style,F.style,b),w&8){const V=u.dynamicProps;for(let J=0;J<V.length;J++){const q=V[J],he=C[q],de=F[q];(de!==he||q==="value")&&i(S,q,he,de,b,p)}}w&1&&f.children!==u.children&&a(S,u.children)}else!E&&x==null&&Je(S,C,F,p,b);(($=F.onVnodeUpdated)||N)&&_e(()=>{$&&Fe($,p,u,f),N&&ct(u,f,p,"updated")},_)},Pe=(f,u,p,_,b,v,E)=>{for(let S=0;S<u.length;S++){const w=f[S],x=u[S],N=w.el&&(w.type===Ue||!It(w,x)||w.shapeFlag&198)?h(w.el):p;O(w,x,N,null,_,b,v,E,!0)}},Je=(f,u,p,_,b)=>{if(u!==p){if(u!==Y)for(const v in u)!jt(v)&&!(v in p)&&i(f,v,u[v],null,b,_);for(const v in p){if(jt(v))continue;const E=p[v],S=u[v];E!==S&&v!=="value"&&i(f,v,S,E,b,_)}"value"in p&&i(f,"value",u.value,p.value,b)}},lt=(f,u,p,_,b,v,E,S,w)=>{const x=u.el=f?f.el:l(""),N=u.anchor=f?f.anchor:l("");let{patchFlag:C,dynamicChildren:F,slotScopeIds:$}=u;$&&(S=S?S.concat($):$),f==null?(s(x,p,_),s(N,p,_),Ce(u.children||[],p,N,b,v,E,S,w)):C>0&&C&64&&F&&f.dynamicChildren?(Pe(f.dynamicChildren,F,p,b,v,E,S),(u.key!=null||b&&u===b.subTree)&&Si(f,u,!0)):B(f,u,p,N,b,v,E,S,w)},Ae=(f,u,p,_,b,v,E,S,w)=>{u.slotScopeIds=S,f==null?u.shapeFlag&512?b.ctx.activate(u,p,_,E,w):Ot(u,p,_,b,v,E,w):dt(f,u,w)},Ot=(f,u,p,_,b,v,E)=>{const S=f.component=$l(f,_,b);if(oi(f)&&(S.ctx.renderer=M),Kl(S,!1,E),S.asyncDep){if(b&&b.registerDep(S,ne,E),!f.el){const w=S.subTree=ge(it);j(null,w,u,p),f.placeholder=w.el}}else ne(S,f,u,p,b,v,E)},dt=(f,u,p)=>{const _=u.component=f.component;if(Cl(f,u,p))if(_.asyncDep&&!_.asyncResolved){G(_,u,p);return}else _.next=u,_.update();else u.el=f.el,_.vnode=u},ne=(f,u,p,_,b,v,E)=>{const S=()=>{if(f.isMounted){let{next:C,bu:F,u:$,parent:V,vnode:J}=f;{const Me=Ei(f);if(Me){C&&(C.el=J.el,G(f,C,E)),Me.asyncDep.then(()=>{f.isUnmounted||S()});return}}let q=C,he;ft(f,!1),C?(C.el=J.el,G(f,C,E)):C=J,F&&rn(F),(he=C.props&&C.props.onVnodeBeforeUpdate)&&Fe(he,V,C,J),ft(f,!0);const de=Us(f),Te=f.subTree;f.subTree=de,O(Te,de,h(Te.el),y(Te),f,b,v),C.el=de.el,q===null&&Pl(f,de.el),$&&_e($,b),(he=C.props&&C.props.onVnodeUpdated)&&_e(()=>Fe(he,V,C,J),b)}else{let C;const{el:F,props:$}=u,{bm:V,m:J,parent:q,root:he,type:de}=f,Te=Lt(u);ft(f,!1),V&&rn(V),!Te&&(C=$&&$.onVnodeBeforeMount)&&Fe(C,q,u),ft(f,!0);{he.ce&&he.ce._def.shadowRoot!==!1&&he.ce._injectChildStyle(de);const Me=f.subTree=Us(f);O(null,Me,p,_,f,b,v),u.el=Me.el}if(J&&_e(J,b),!Te&&(C=$&&$.onVnodeMounted)){const Me=u;_e(()=>Fe(C,q,Me),b)}(u.shapeFlag&256||q&&Lt(q.vnode)&&q.vnode.shapeFlag&256)&&f.a&&_e(f.a,b),f.isMounted=!0,u=p=_=null}};f.scope.on();const w=f.effect=new Fr(S);f.scope.off();const x=f.update=w.run.bind(w),N=f.job=w.runIfDirty.bind(w);N.i=f,N.id=f.uid,w.scheduler=()=>xs(N),ft(f,!0),x()},G=(f,u,p)=>{u.component=f;const _=f.vnode.props;f.vnode=u,f.next=null,ul(f,u.props,_,p),pl(f,u.children,p),Ge(),js(f),ze()},B=(f,u,p,_,b,v,E,S,w=!1)=>{const x=f&&f.children,N=f?f.shapeFlag:0,C=u.children,{patchFlag:F,shapeFlag:$}=u;if(F>0){if(F&128){Xe(x,C,p,_,b,v,E,S,w);return}else if(F&256){Le(x,C,p,_,b,v,E,S,w);return}}$&8?(N&16&&be(x,b,v),C!==x&&a(p,C)):N&16?$&16?Xe(x,C,p,_,b,v,E,S,w):be(x,b,v,!0):(N&8&&a(p,""),$&16&&Ce(C,p,_,b,v,E,S,w))},Le=(f,u,p,_,b,v,E,S,w)=>{f=f||vt,u=u||vt;const x=f.length,N=u.length,C=Math.min(x,N);let F;for(F=0;F<C;F++){const $=u[F]=w?tt(u[F]):je(u[F]);O(f[F],$,p,null,b,v,E,S,w)}x>N?be(f,b,v,!0,!1,C):Ce(u,p,_,b,v,E,S,w,C)},Xe=(f,u,p,_,b,v,E,S,w)=>{let x=0;const N=u.length;let C=f.length-1,F=N-1;for(;x<=C&&x<=F;){const $=f[x],V=u[x]=w?tt(u[x]):je(u[x]);if(It($,V))O($,V,p,null,b,v,E,S,w);else break;x++}for(;x<=C&&x<=F;){const $=f[C],V=u[F]=w?tt(u[F]):je(u[F]);if(It($,V))O($,V,p,null,b,v,E,S,w);else break;C--,F--}if(x>C){if(x<=F){const $=F+1,V=$<N?u[$].el:_;for(;x<=F;)O(null,u[x]=w?tt(u[x]):je(u[x]),p,V,b,v,E,S,w),x++}}else if(x>F)for(;x<=C;)ae(f[x],b,v,!0),x++;else{const $=x,V=x,J=new Map;for(x=V;x<=F;x++){const me=u[x]=w?tt(u[x]):je(u[x]);me.key!=null&&J.set(me.key,x)}let q,he=0;const de=F-V+1;let Te=!1,Me=0;const Tt=new Array(de);for(x=0;x<de;x++)Tt[x]=0;for(x=$;x<=C;x++){const me=f[x];if(he>=de){ae(me,b,v,!0);continue}let Ie;if(me.key!=null)Ie=J.get(me.key);else for(q=V;q<=F;q++)if(Tt[q-V]===0&&It(me,u[q])){Ie=q;break}Ie===void 0?ae(me,b,v,!0):(Tt[Ie-V]=x+1,Ie>=Me?Me=Ie:Te=!0,O(me,u[Ie],p,null,b,v,E,S,w),he++)}const As=Te?yl(Tt):vt;for(q=As.length-1,x=de-1;x>=0;x--){const me=V+x,Ie=u[me],Os=u[me+1],Ts=me+1<N?Os.el||Os.placeholder:_;Tt[x]===0?O(null,Ie,p,Ts,b,v,E,S,w):Te&&(q<0||x!==As[q]?Oe(Ie,p,Ts,2):q--)}}},Oe=(f,u,p,_,b=null)=>{const{el:v,type:E,transition:S,children:w,shapeFlag:x}=f;if(x&6){Oe(f.component.subTree,u,p,_);return}if(x&128){f.suspense.move(u,p,_);return}if(x&64){E.move(f,u,p,M);return}if(E===Ue){s(v,u,p);for(let C=0;C<w.length;C++)Oe(w[C],u,p,_);s(f.anchor,u,p);return}if(E===cn){D(f,u,p);return}if(_!==2&&x&1&&S)if(_===0)S.beforeEnter(v),s(v,u,p),_e(()=>S.enter(v),b);else{const{leave:C,delayLeave:F,afterLeave:$}=S,V=()=>{f.ctx.isUnmounted?r(v):s(v,u,p)},J=()=>{C(v,()=>{V(),$&&$()})};F?F(v,V,J):J()}else s(v,u,p)},ae=(f,u,p,_=!1,b=!1)=>{const{type:v,props:E,ref:S,children:w,dynamicChildren:x,shapeFlag:N,patchFlag:C,dirs:F,cacheIndex:$}=f;if(C===-2&&(b=!1),S!=null&&(Ge(),$t(S,null,p,f,!0),ze()),$!=null&&(u.renderCache[$]=void 0),N&256){u.ctx.deactivate(f);return}const V=N&1&&F,J=!Lt(f);let q;if(J&&(q=E&&E.onVnodeBeforeUnmount)&&Fe(q,u,f),N&6)en(f.component,p,_);else{if(N&128){f.suspense.unmount(p,_);return}V&&ct(f,null,u,"beforeUnmount"),N&64?f.type.remove(f,u,p,M,_):x&&!x.hasOnce&&(v!==Ue||C>0&&C&64)?be(x,u,p,!1,!0):(v===Ue&&C&384||!b&&N&16)&&be(w,u,p),_&&pt(f)}(J&&(q=E&&E.onVnodeUnmounted)||V)&&_e(()=>{q&&Fe(q,u,f),V&&ct(f,null,u,"unmounted")},p)},pt=f=>{const{type:u,el:p,anchor:_,transition:b}=f;if(u===Ue){gt(p,_);return}if(u===cn){T(f);return}const v=()=>{r(p),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(f.shapeFlag&1&&b&&!b.persisted){const{leave:E,delayLeave:S}=b,w=()=>E(p,v);S?S(f.el,v,w):w()}else v()},gt=(f,u)=>{let p;for(;f!==u;)p=g(f),r(f),f=p;r(u)},en=(f,u,p)=>{const{bum:_,scope:b,job:v,subTree:E,um:S,m:w,a:x,parent:N,slots:{__:C}}=f;Bs(w),Bs(x),_&&rn(_),N&&H(C)&&C.forEach(F=>{N.renderCache[F]=void 0}),b.stop(),v&&(v.flags|=8,ae(E,f,u,p)),S&&_e(S,u),_e(()=>{f.isUnmounted=!0},u),u&&u.pendingBranch&&!u.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===u.pendingId&&(u.deps--,u.deps===0&&u.resolve())},be=(f,u,p,_=!1,b=!1,v=0)=>{for(let E=v;E<f.length;E++)ae(f[E],u,p,_,b)},y=f=>{if(f.shapeFlag&6)return y(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const u=g(f.anchor||f.el),p=u&&u[Vo];return p?g(p):u};let P=!1;const R=(f,u,p)=>{f==null?u._vnode&&ae(u._vnode,null,null,!0):O(u._vnode||null,f,u,null,null,null,p),u._vnode=f,P||(P=!0,js(),ti(),P=!1)},M={p:O,um:ae,m:Oe,r:pt,mt:Ot,mc:Ce,pc:B,pbc:Pe,n:y,o:e};return{render:R,hydrate:void 0,createApp:cl(R)}}function Vn({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function ft({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function _l(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Si(e,t,n=!1){const s=e.children,r=t.children;if(H(s)&&H(r))for(let i=0;i<s.length;i++){const o=s[i];let l=r[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[i]=tt(r[i]),l.el=o.el),!n&&l.patchFlag!==-2&&Si(o,l)),l.type===In&&(l.el=o.el),l.type===it&&!l.el&&(l.el=o.el)}}function yl(e){const t=e.slice(),n=[0];let s,r,i,o,l;const c=e.length;for(s=0;s<c;s++){const d=e[s];if(d!==0){if(r=n[n.length-1],e[r]<d){t[s]=r,n.push(s);continue}for(i=0,o=n.length-1;i<o;)l=i+o>>1,e[n[l]]<d?i=l+1:o=l;d<e[n[i]]&&(i>0&&(t[s]=n[i-1]),n[i]=s)}}for(i=n.length,o=n[i-1];i-- >0;)n[i]=o,o=t[o];return n}function Ei(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ei(t)}function Bs(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const vl=Symbol.for("v-scx"),bl=()=>qe(vl);function ln(e,t,n){return Ri(e,t,n)}function Ri(e,t,n=Y){const{immediate:s,deep:r,flush:i,once:o}=n,l=ce({},n),c=t&&s||!t&&i!=="post";let d;if(zt){if(i==="sync"){const m=bl();d=m.__watcherHandles||(m.__watcherHandles=[])}else if(!c){const m=()=>{};return m.stop=De,m.resume=De,m.pause=De,m}}const a=oe;l.call=(m,A,O)=>$e(m,a,A,O);let h=!1;i==="post"?l.scheduler=m=>{_e(m,a&&a.suspense)}:i!=="sync"&&(h=!0,l.scheduler=(m,A)=>{A?m():xs(m)}),l.augmentJob=m=>{t&&(m.flags|=4),h&&(m.flags|=2,a&&(m.id=a.uid,m.i=a))};const g=Do(e,t,l);return zt&&(d?d.push(g):c&&g()),g}function xl(e,t,n){const s=this.proxy,r=te(e)?e.includes(".")?Ci(s,e):()=>s[e]:e.bind(s,s);let i;L(t)?i=t:(i=t.handler,n=t);const o=Zt(this),l=Ri(r,i.bind(s),n);return o(),l}function Ci(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const wl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e["".concat(t,"Modifiers")]||e["".concat(we(t),"Modifiers")]||e["".concat(ot(t),"Modifiers")];function Sl(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||Y;let r=n;const i=t.startsWith("update:"),o=i&&wl(s,t.slice(7));o&&(o.trim&&(r=n.map(a=>te(a)?a.trim():a)),o.number&&(r=n.map(an)));let l,c=s[l=jn(t)]||s[l=jn(we(t))];!c&&i&&(c=s[l=jn(ot(t))]),c&&$e(c,e,6,r);const d=s[l+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,$e(d,e,6,r)}}function Pi(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const i=e.emits;let o={},l=!1;if(!L(e)){const c=d=>{const a=Pi(d,t,!0);a&&(l=!0,ce(o,a))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!i&&!l?(Z(e)&&s.set(e,null),null):(H(i)?i.forEach(c=>o[c]=null):ce(o,i),Z(e)&&s.set(e,o),o)}function Mn(e,t){return!e||!xn(t)?!1:(t=t.slice(2).replace(/Once$/,""),W(e,t[0].toLowerCase()+t.slice(1))||W(e,ot(t))||W(e,t))}function Us(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[i],slots:o,attrs:l,emit:c,render:d,renderCache:a,props:h,data:g,setupState:m,ctx:A,inheritAttrs:O}=e,K=gn(e);let j,I;try{if(n.shapeFlag&4){const T=r||s,z=T;j=je(d.call(z,T,a,h,m,g,A)),I=l}else{const T=t;j=je(T.length>1?T(h,{attrs:l,slots:o,emit:c}):T(h,null)),I=t.props?l:El(l)}}catch(T){Vt.length=0,On(T,e,1),j=ge(it)}let D=j;if(I&&O!==!1){const T=Object.keys(I),{shapeFlag:z}=D;T.length&&z&7&&(i&&T.some(cs)&&(I=Rl(I,i)),D=Ct(D,I,!1,!0))}return n.dirs&&(D=Ct(D,null,!1,!0),D.dirs=D.dirs?D.dirs.concat(n.dirs):n.dirs),n.transition&&ws(D,n.transition),j=D,gn(K),j}const El=e=>{let t;for(const n in e)(n==="class"||n==="style"||xn(n))&&((t||(t={}))[n]=e[n]);return t},Rl=(e,t)=>{const n={};for(const s in e)(!cs(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Cl(e,t,n){const{props:s,children:r,component:i}=e,{props:o,children:l,patchFlag:c}=t,d=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?ks(s,o,d):!!o;if(c&8){const a=t.dynamicProps;for(let h=0;h<a.length;h++){const g=a[h];if(o[g]!==s[g]&&!Mn(d,g))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===o?!1:s?o?ks(s,o,d):!0:!!o;return!1}function ks(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const i=s[r];if(t[i]!==e[i]&&!Mn(n,i))return!0}return!1}function Pl({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Ai=e=>e.__isSuspense;function Al(e,t){t&&t.pendingBranch?H(e)?t.effects.push(...e):t.effects.push(e):Lo(e)}const Ue=Symbol.for("v-fgt"),In=Symbol.for("v-txt"),it=Symbol.for("v-cmt"),cn=Symbol.for("v-stc"),Vt=[];let ve=null;function Ol(e=!1){Vt.push(ve=e?null:[])}function Tl(){Vt.pop(),ve=Vt[Vt.length-1]||null}let Gt=1;function Ws(e,t=!1){Gt+=e,e<0&&ve&&t&&(ve.hasOnce=!0)}function Oi(e){return e.dynamicChildren=Gt>0?ve||vt:null,Tl(),Gt>0&&ve&&ve.push(e),e}function Pf(e,t,n,s,r,i){return Oi(Mi(e,t,n,s,r,i,!0))}function Ml(e,t,n,s,r){return Oi(ge(e,t,n,s,r,!0))}function _n(e){return e?e.__v_isVNode===!0:!1}function It(e,t){return e.type===t.type&&e.key===t.key}const Ti=({key:e})=>e!=null?e:null,fn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?te(e)||le(e)||L(e)?{i:ye,r:e,k:t,f:!!n}:e:null);function Mi(e,t=null,n=null,s=0,r=null,i=e===Ue?0:1,o=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ti(t),ref:t&&fn(t),scopeId:si,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:ye};return l?(Rs(c,n),i&128&&e.normalize(c)):n&&(c.shapeFlag|=te(n)?8:16),Gt>0&&!o&&ve&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&ve.push(c),c}const ge=Il;function Il(e,t=null,n=null,s=0,r=null,i=!1){if((!e||e===ui)&&(e=it),_n(e)){const l=Ct(e,t,!0);return n&&Rs(l,n),Gt>0&&!i&&ve&&(l.shapeFlag&6?ve[ve.indexOf(e)]=l:ve.push(l)),l.patchFlag=-2,l}if(Wl(e)&&(e=e.__vccOpts),t){t=Fl(t);let{class:l,style:c}=t;l&&!te(l)&&(t.class=hs(l)),Z(c)&&(vs(c)&&!H(c)&&(c=ce({},c)),t.style=as(c))}const o=te(e)?1:Ai(e)?128:Bo(e)?64:Z(e)?4:L(e)?2:0;return Mi(e,t,n,s,r,o,i,!0)}function Fl(e){return e?vs(e)||_i(e)?ce({},e):e:null}function Ct(e,t,n=!1,s=!1){const{props:r,ref:i,patchFlag:o,children:l,transition:c}=e,d=t?jl(r||{},t):r,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&Ti(d),ref:t&&t.ref?n&&i?H(i)?i.concat(fn(t)):[i,fn(t)]:fn(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ue?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ct(e.ssContent),ssFallback:e.ssFallback&&Ct(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&ws(a,c.clone(a)),a}function Nl(e=" ",t=0){return ge(In,null,e,t)}function Af(e,t){const n=ge(cn,null,e);return n.staticCount=t,n}function Of(e="",t=!1){return t?(Ol(),Ml(it,null,e)):ge(it,null,e)}function je(e){return e==null||typeof e=="boolean"?ge(it):H(e)?ge(Ue,null,e.slice()):_n(e)?tt(e):ge(In,null,String(e))}function tt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ct(e)}function Rs(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(H(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Rs(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!_i(t)?t._ctx=ye:r===3&&ye&&(ye.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else L(t)?(t={default:t,_ctx:ye},n=32):(t=String(t),s&64?(n=16,t=[Nl(t)]):n=8);e.children=t,e.shapeFlag|=n}function jl(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=hs([t.class,s.class]));else if(r==="style")t.style=as([t.style,s.style]);else if(xn(r)){const i=t[r],o=s[r];o&&i!==o&&!(H(i)&&i.includes(o))&&(t[r]=i?[].concat(i,o):o)}else r!==""&&(t[r]=s[r])}return t}function Fe(e,t,n,s=null){$e(e,t,7,[n,s])}const Dl=pi();let Hl=0;function $l(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||Dl,i={uid:Hl++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Ir(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:vi(s,r),emitsOptions:Pi(s,r),emit:null,emitted:null,propsDefaults:Y,inheritAttrs:s.inheritAttrs,ctx:Y,data:Y,props:Y,attrs:Y,slots:Y,refs:Y,setupState:Y,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Sl.bind(null,i),e.ce&&e.ce(i),i}let oe=null;const Ll=()=>oe||ye;let yn,ns;{const e=Rn(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),i=>{r.length>1?r.forEach(o=>o(i)):r[0](i)}};yn=t("__VUE_INSTANCE_SETTERS__",n=>oe=n),ns=t("__VUE_SSR_SETTERS__",n=>zt=n)}const Zt=e=>{const t=oe;return yn(e),e.scope.on(),()=>{e.scope.off(),yn(t)}},qs=()=>{oe&&oe.scope.off(),yn(null)};function Ii(e){return e.vnode.shapeFlag&4}let zt=!1;function Kl(e,t=!1,n=!1){t&&ns(t);const{props:s,children:r}=e.vnode,i=Ii(e);fl(e,s,i,t),dl(e,r,n||t);const o=i?Vl(e,t):void 0;return t&&ns(!1),o}function Vl(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,tl);const{setup:s}=n;if(s){Ge();const r=e.setupContext=s.length>1?Ul(e):null,i=Zt(e),o=Xt(s,e,0,[e.props,r]),l=Cr(o);if(ze(),i(),(l||e.sp)&&!Lt(e)&&ii(e),l){if(o.then(qs,qs),t)return o.then(c=>{Gs(e,c)}).catch(c=>{On(c,e,0)});e.asyncDep=o}else Gs(e,o)}else Fi(e)}function Gs(e,t,n){L(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Z(t)&&(e.setupState=Xr(t)),Fi(e)}function Fi(e,t,n){const s=e.type;e.render||(e.render=s.render||De);{const r=Zt(e);Ge();try{nl(e)}finally{ze(),r()}}}const Bl={get(e,t){return ie(e,"get",""),e[t]}};function Ul(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Bl),slots:e.slots,emit:e.emit,expose:t}}function Fn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Xr(Qr(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Kt)return Kt[n](e)},has(t,n){return n in t||n in Kt}})):e.proxy}function kl(e,t=!0){return L(e)?e.displayName||e.name:e.name||t&&e.__name}function Wl(e){return L(e)&&"__vccOpts"in e}const Se=(e,t)=>No(e,t,zt);function Ni(e,t,n){const s=arguments.length;return s===2?Z(t)&&!H(t)?_n(t)?ge(e,null,[t]):ge(e,t):ge(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&_n(n)&&(n=[n]),ge(e,t,n))}const ql="3.5.18";/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ss;const zs=typeof window<"u"&&window.trustedTypes;if(zs)try{ss=zs.createPolicy("vue",{createHTML:e=>e})}catch(e){}const ji=ss?e=>ss.createHTML(e):e=>e,Gl="http://www.w3.org/2000/svg",zl="http://www.w3.org/1998/Math/MathML",Be=typeof document<"u"?document:null,Qs=Be&&Be.createElement("template"),Ql={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?Be.createElementNS(Gl,e):t==="mathml"?Be.createElementNS(zl,e):n?Be.createElement(e,{is:n}):Be.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>Be.createTextNode(e),createComment:e=>Be.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Be.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,i){const o=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===i||!(r=r.nextSibling)););else{Qs.innerHTML=ji(s==="svg"?"<svg>".concat(e,"</svg>"):s==="mathml"?"<math>".concat(e,"</math>"):e);const l=Qs.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Yl=Symbol("_vtc");function Jl(e,t,n){const s=e[Yl];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Ys=Symbol("_vod"),Xl=Symbol("_vsh"),Zl=Symbol(""),ec=/(^|;)\s*display\s*:/;function tc(e,t,n){const s=e.style,r=te(n);let i=!1;if(n&&!r){if(t)if(te(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();n[l]==null&&un(s,l,"")}else for(const o in t)n[o]==null&&un(s,o,"");for(const o in n)o==="display"&&(i=!0),un(s,o,n[o])}else if(r){if(t!==n){const o=s[Zl];o&&(n+=";"+o),s.cssText=n,i=ec.test(n)}}else t&&e.removeAttribute("style");Ys in e&&(e[Ys]=i?s.display:"",e[Xl]&&(s.display="none"))}const Js=/\s*!important$/;function un(e,t,n){if(H(n))n.forEach(s=>un(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=nc(e,t);Js.test(n)?e.setProperty(ot(s),n.replace(Js,""),"important"):e[s]=n}}const Xs=["Webkit","Moz","ms"],Bn={};function nc(e,t){const n=Bn[t];if(n)return n;let s=we(t);if(s!=="filter"&&s in e)return Bn[t]=s;s=En(s);for(let r=0;r<Xs.length;r++){const i=Xs[r]+s;if(i in e)return Bn[t]=i}return t}const Zs="http://www.w3.org/1999/xlink";function er(e,t,n,s,r,i=io(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Zs,t.slice(6,t.length)):e.setAttributeNS(Zs,t,n):n==null||i&&!Or(n)?e.removeAttribute(t):e.setAttribute(t,i?"":He(n)?String(n):n)}function tr(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?ji(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const l=i==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Or(n):n==null&&l==="string"?(n="",o=!0):l==="number"&&(n=0,o=!0)}try{e[t]=n}catch(l){}o&&e.removeAttribute(r||t)}function at(e,t,n,s){e.addEventListener(t,n,s)}function sc(e,t,n,s){e.removeEventListener(t,n,s)}const nr=Symbol("_vei");function rc(e,t,n,s,r=null){const i=e[nr]||(e[nr]={}),o=i[t];if(s&&o)o.value=s;else{const[l,c]=ic(t);if(s){const d=i[t]=cc(s,r);at(e,l,d,c)}else o&&(sc(e,l,o,c),i[t]=void 0)}}const sr=/(?:Once|Passive|Capture)$/;function ic(e){let t;if(sr.test(e)){t={};let s;for(;s=e.match(sr);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):ot(e.slice(2)),t]}let Un=0;const oc=Promise.resolve(),lc=()=>Un||(oc.then(()=>Un=0),Un=Date.now());function cc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;$e(fc(s,n.value),t,5,[s])};return n.value=e,n.attached=lc(),n}function fc(e,t){if(H(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const rr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,uc=(e,t,n,s,r,i)=>{const o=r==="svg";t==="class"?Jl(e,s,o):t==="style"?tc(e,n,s):xn(t)?cs(t)||rc(e,t,n,s,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):ac(e,t,s,o))?(tr(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&er(e,t,s,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!te(s))?tr(e,we(t),s,i,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),er(e,t,s,o))};function ac(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&rr(t)&&L(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return rr(t)&&te(n)?!1:t in e}const vn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return H(t)?n=>rn(t,n):t};function hc(e){e.target.composing=!0}function ir(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Rt=Symbol("_assign"),Tf={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[Rt]=vn(r);const i=s||r.props&&r.props.type==="number";at(e,t?"change":"input",o=>{if(o.target.composing)return;let l=e.value;n&&(l=l.trim()),i&&(l=an(l)),e[Rt](l)}),n&&at(e,"change",()=>{e.value=e.value.trim()}),t||(at(e,"compositionstart",hc),at(e,"compositionend",ir),at(e,"change",ir))},mounted(e,{value:t}){e.value=t==null?"":t},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:i}},o){if(e[Rt]=vn(o),e.composing)return;const l=(i||e.type==="number")&&!/^0\d/.test(e.value)?an(e.value):e.value,c=t==null?"":t;l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===c)||(e.value=c))}},Mf={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=wn(t);at(e,"change",()=>{const i=Array.prototype.filter.call(e.options,o=>o.selected).map(o=>n?an(bn(o)):bn(o));e[Rt](e.multiple?r?new Set(i):i:i[0]),e._assigning=!0,bs(()=>{e._assigning=!1})}),e[Rt]=vn(s)},mounted(e,{value:t}){or(e,t)},beforeUpdate(e,t,n){e[Rt]=vn(n)},updated(e,{value:t}){e._assigning||or(e,t)}};function or(e,t){const n=e.multiple,s=H(t);if(!(n&&!s&&!wn(t))){for(let r=0,i=e.options.length;r<i;r++){const o=e.options[r],l=bn(o);if(n)if(s){const c=typeof l;c==="string"||c==="number"?o.selected=t.some(d=>String(d)===String(l)):o.selected=lo(t,l)>-1}else o.selected=t.has(l);else if(Cn(bn(o),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function bn(e){return"_value"in e?e._value:e.value}const dc=["ctrl","shift","alt","meta"],pc={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>dc.some(n=>e["".concat(n,"Key")]&&!t.includes(n))},If=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...i)=>{for(let o=0;o<t.length;o++){const l=pc[t[o]];if(l&&l(r,t))return}return e(r,...i)})},gc={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Ff=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=r=>{if(!("key"in r))return;const i=ot(r.key);if(t.some(o=>o===i||gc[o]===i))return e(r)})},mc=ce({patchProp:uc},Ql);let lr;function _c(){return lr||(lr=gl(mc))}const Nf=(...e)=>{const t=_c().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=vc(s);if(!r)return;const i=t._component;!L(i)&&!i.render&&!i.template&&(i.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const o=n(r,!1,yc(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),o},t};function yc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function vc(e){return te(e)?document.querySelector(e):e}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const bc=Symbol();var cr;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(cr||(cr={}));function jf(){const e=fo(!0),t=e.run(()=>Yr({}));let n=[],s=[];const r=Qr({install(i){r._a=i,i.provide(bc,r),i.config.globalProperties.$pinia=r,s.forEach(o=>n.push(o)),s=[]},use(i){return this._a?n.push(i):s.push(i),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const yt=typeof document<"u";function Di(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function xc(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Di(e.default)}const U=Object.assign;function kn(e,t){const n={};for(const s in t){const r=t[s];n[s]=Re(r)?r.map(e):e(r)}return n}const Bt=()=>{},Re=Array.isArray,Hi=/#/g,wc=/&/g,Sc=/\//g,Ec=/=/g,Rc=/\?/g,$i=/\+/g,Cc=/%5B/g,Pc=/%5D/g,Li=/%5E/g,Ac=/%60/g,Ki=/%7B/g,Oc=/%7C/g,Vi=/%7D/g,Tc=/%20/g;function Cs(e){return encodeURI(""+e).replace(Oc,"|").replace(Cc,"[").replace(Pc,"]")}function Mc(e){return Cs(e).replace(Ki,"{").replace(Vi,"}").replace(Li,"^")}function rs(e){return Cs(e).replace($i,"%2B").replace(Tc,"+").replace(Hi,"%23").replace(wc,"%26").replace(Ac,"`").replace(Ki,"{").replace(Vi,"}").replace(Li,"^")}function Ic(e){return rs(e).replace(Ec,"%3D")}function Fc(e){return Cs(e).replace(Hi,"%23").replace(Rc,"%3F")}function Nc(e){return e==null?"":Fc(e).replace(Sc,"%2F")}function Qt(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const jc=/\/$/,Dc=e=>e.replace(jc,"");function Wn(e,t,n="/"){let s,r={},i="",o="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),i=t.slice(c+1,l>-1?l:t.length),r=e(i)),l>-1&&(s=s||t.slice(0,l),o=t.slice(l,t.length)),s=Kc(s!=null?s:t,n),{fullPath:s+(i&&"?")+i+o,path:s,query:r,hash:Qt(o)}}function Hc(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function fr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function $c(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Pt(t.matched[s],n.matched[r])&&Bi(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Pt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Bi(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Lc(e[n],t[n]))return!1;return!0}function Lc(e,t){return Re(e)?ur(e,t):Re(t)?ur(t,e):e===t}function ur(e,t){return Re(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Kc(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let i=n.length-1,o,l;for(o=0;o<s.length;o++)if(l=s[o],l!==".")if(l==="..")i>1&&i--;else break;return n.slice(0,i).join("/")+"/"+s.slice(o).join("/")}const Ze={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Yt;(function(e){e.pop="pop",e.push="push"})(Yt||(Yt={}));var Ut;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Ut||(Ut={}));function Vc(e){if(!e)if(yt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Dc(e)}const Bc=/^[^#]+#/;function Uc(e,t){return e.replace(Bc,"#")+t}function kc(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Nn=()=>({left:window.scrollX,top:window.scrollY});function Wc(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=kc(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function ar(e,t){return(history.state?history.state.position-t:-1)+e}const is=new Map;function qc(e,t){is.set(e,t)}function Gc(e){const t=is.get(e);return is.delete(e),t}let zc=()=>location.protocol+"//"+location.host;function Ui(e,t){const{pathname:n,search:s,hash:r}=t,i=e.indexOf("#");if(i>-1){let l=r.includes(e.slice(i))?e.slice(i).length:1,c=r.slice(l);return c[0]!=="/"&&(c="/"+c),fr(c,"")}return fr(n,e)+s+r}function Qc(e,t,n,s){let r=[],i=[],o=null;const l=({state:g})=>{const m=Ui(e,location),A=n.value,O=t.value;let K=0;if(g){if(n.value=m,t.value=g,o&&o===A){o=null;return}K=O?g.position-O.position:0}else s(m);r.forEach(j=>{j(n.value,A,{delta:K,type:Yt.pop,direction:K?K>0?Ut.forward:Ut.back:Ut.unknown})})};function c(){o=n.value}function d(g){r.push(g);const m=()=>{const A=r.indexOf(g);A>-1&&r.splice(A,1)};return i.push(m),m}function a(){const{history:g}=window;g.state&&g.replaceState(U({},g.state,{scroll:Nn()}),"")}function h(){for(const g of i)g();i=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:c,listen:d,destroy:h}}function hr(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?Nn():null}}function Yc(e){const{history:t,location:n}=window,s={value:Ui(e,n)},r={value:t.state};r.value||i(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(c,d,a){const h=e.indexOf("#"),g=h>-1?(n.host&&document.querySelector("base")?e:e.slice(h))+c:zc()+e+c;try{t[a?"replaceState":"pushState"](d,"",g),r.value=d}catch(m){console.error(m),n[a?"replace":"assign"](g)}}function o(c,d){const a=U({},t.state,hr(r.value.back,c,r.value.forward,!0),d,{position:r.value.position});i(c,a,!0),s.value=c}function l(c,d){const a=U({},r.value,t.state,{forward:c,scroll:Nn()});i(a.current,a,!0);const h=U({},hr(s.value,c,null),{position:a.position+1},d);i(c,h,!1),s.value=c}return{location:s,state:r,push:l,replace:o}}function Df(e){e=Vc(e);const t=Yc(e),n=Qc(e,t.state,t.location,t.replace);function s(i,o=!0){o||n.pauseListeners(),history.go(i)}const r=U({location:"",base:e,go:s,createHref:Uc.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Jc(e){return typeof e=="string"||e&&typeof e=="object"}function ki(e){return typeof e=="string"||typeof e=="symbol"}const Wi=Symbol("");var dr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(dr||(dr={}));function At(e,t){return U(new Error,{type:e,[Wi]:!0},t)}function Ve(e,t){return e instanceof Error&&Wi in e&&(t==null||!!(e.type&t))}const pr="[^/]+?",Xc={sensitive:!1,strict:!1,start:!0,end:!0},Zc=/[.+*?^${}()[\]/\\]/g;function ef(e,t){const n=U({},Xc,t),s=[];let r=n.start?"^":"";const i=[];for(const d of e){const a=d.length?[]:[90];n.strict&&!d.length&&(r+="/");for(let h=0;h<d.length;h++){const g=d[h];let m=40+(n.sensitive?.25:0);if(g.type===0)h||(r+="/"),r+=g.value.replace(Zc,"\\$&"),m+=40;else if(g.type===1){const{value:A,repeatable:O,optional:K,regexp:j}=g;i.push({name:A,repeatable:O,optional:K});const I=j||pr;if(I!==pr){m+=10;try{new RegExp("(".concat(I,")"))}catch(T){throw new Error('Invalid custom RegExp for param "'.concat(A,'" (').concat(I,"): ")+T.message)}}let D=O?"((?:".concat(I,")(?:/(?:").concat(I,"))*)"):"(".concat(I,")");h||(D=K&&d.length<2?"(?:/".concat(D,")"):"/"+D),K&&(D+="?"),r+=D,m+=20,K&&(m+=-8),O&&(m+=-20),I===".*"&&(m+=-50)}a.push(m)}s.push(a)}if(n.strict&&n.end){const d=s.length-1;s[d][s[d].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const o=new RegExp(r,n.sensitive?"":"i");function l(d){const a=d.match(o),h={};if(!a)return null;for(let g=1;g<a.length;g++){const m=a[g]||"",A=i[g-1];h[A.name]=m&&A.repeatable?m.split("/"):m}return h}function c(d){let a="",h=!1;for(const g of e){(!h||!a.endsWith("/"))&&(a+="/"),h=!1;for(const m of g)if(m.type===0)a+=m.value;else if(m.type===1){const{value:A,repeatable:O,optional:K}=m,j=A in d?d[A]:"";if(Re(j)&&!O)throw new Error('Provided param "'.concat(A,'" is an array but it is not repeatable (* or + modifiers)'));const I=Re(j)?j.join("/"):j;if(!I)if(K)g.length<2&&(a.endsWith("/")?a=a.slice(0,-1):h=!0);else throw new Error('Missing required param "'.concat(A,'"'));a+=I}}return a||"/"}return{re:o,score:s,keys:i,parse:l,stringify:c}}function tf(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function qi(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const i=tf(s[n],r[n]);if(i)return i;n++}if(Math.abs(r.length-s.length)===1){if(gr(s))return 1;if(gr(r))return-1}return r.length-s.length}function gr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const nf={type:0,value:""},sf=/[a-zA-Z0-9_]/;function rf(e){if(!e)return[[]];if(e==="/")return[[nf]];if(!e.startsWith("/"))throw new Error('Invalid path "'.concat(e,'"'));function t(m){throw new Error("ERR (".concat(n,')/"').concat(d,'": ').concat(m))}let n=0,s=n;const r=[];let i;function o(){i&&r.push(i),i=[]}let l=0,c,d="",a="";function h(){d&&(n===0?i.push({type:0,value:d}):n===1||n===2||n===3?(i.length>1&&(c==="*"||c==="+")&&t("A repeatable param (".concat(d,") must be alone in its segment. eg: '/:ids+.")),i.push({type:1,value:d,regexp:a,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),d="")}function g(){d+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(d&&h(),o()):c===":"?(h(),n=1):g();break;case 4:g(),n=s;break;case 1:c==="("?n=2:sf.test(c)?g():(h(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+c:n=3:a+=c;break;case 3:h(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,a="";break;default:t("Unknown state");break}}return n===2&&t('Unfinished custom RegExp for param "'.concat(d,'"')),h(),o(),r}function of(e,t,n){const s=ef(rf(e.path),n),r=U(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function lf(e,t){const n=[],s=new Map;t=vr({strict:!1,end:!0,sensitive:!1},t);function r(h){return s.get(h)}function i(h,g,m){const A=!m,O=_r(h);O.aliasOf=m&&m.record;const K=vr(t,h),j=[O];if("alias"in h){const T=typeof h.alias=="string"?[h.alias]:h.alias;for(const z of T)j.push(_r(U({},O,{components:m?m.record.components:O.components,path:z,aliasOf:m?m.record:O})))}let I,D;for(const T of j){const{path:z}=T;if(g&&z[0]!=="/"){const se=g.record.path,ee=se[se.length-1]==="/"?"":"/";T.path=g.record.path+(z&&ee+z)}if(I=of(T,g,K),m?m.alias.push(I):(D=D||I,D!==I&&D.alias.push(I),A&&h.name&&!yr(I)&&o(h.name)),Gi(I)&&c(I),O.children){const se=O.children;for(let ee=0;ee<se.length;ee++)i(se[ee],I,m&&m.children[ee])}m=m||I}return D?()=>{o(D)}:Bt}function o(h){if(ki(h)){const g=s.get(h);g&&(s.delete(h),n.splice(n.indexOf(g),1),g.children.forEach(o),g.alias.forEach(o))}else{const g=n.indexOf(h);g>-1&&(n.splice(g,1),h.record.name&&s.delete(h.record.name),h.children.forEach(o),h.alias.forEach(o))}}function l(){return n}function c(h){const g=uf(h,n);n.splice(g,0,h),h.record.name&&!yr(h)&&s.set(h.record.name,h)}function d(h,g){let m,A={},O,K;if("name"in h&&h.name){if(m=s.get(h.name),!m)throw At(1,{location:h});K=m.record.name,A=U(mr(g.params,m.keys.filter(D=>!D.optional).concat(m.parent?m.parent.keys.filter(D=>D.optional):[]).map(D=>D.name)),h.params&&mr(h.params,m.keys.map(D=>D.name))),O=m.stringify(A)}else if(h.path!=null)O=h.path,m=n.find(D=>D.re.test(O)),m&&(A=m.parse(O),K=m.record.name);else{if(m=g.name?s.get(g.name):n.find(D=>D.re.test(g.path)),!m)throw At(1,{location:h,currentLocation:g});K=m.record.name,A=U({},g.params,h.params),O=m.stringify(A)}const j=[];let I=m;for(;I;)j.unshift(I.record),I=I.parent;return{name:K,path:O,params:A,matched:j,meta:ff(j)}}e.forEach(h=>i(h));function a(){n.length=0,s.clear()}return{addRoute:i,resolve:d,removeRoute:o,clearRoutes:a,getRoutes:l,getRecordMatcher:r}}function mr(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function _r(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:cf(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function cf(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function yr(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function ff(e){return e.reduce((t,n)=>U(t,n.meta),{})}function vr(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function uf(e,t){let n=0,s=t.length;for(;n!==s;){const i=n+s>>1;qi(e,t[i])<0?s=i:n=i+1}const r=af(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function af(e){let t=e;for(;t=t.parent;)if(Gi(t)&&qi(e,t)===0)return t}function Gi({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function hf(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const i=s[r].replace($i," "),o=i.indexOf("="),l=Qt(o<0?i:i.slice(0,o)),c=o<0?null:Qt(i.slice(o+1));if(l in t){let d=t[l];Re(d)||(d=t[l]=[d]),d.push(c)}else t[l]=c}return t}function br(e){let t="";for(let n in e){const s=e[n];if(n=Ic(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Re(s)?s.map(i=>i&&rs(i)):[s&&rs(s)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+n,i!=null&&(t+="="+i))})}return t}function df(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Re(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const pf=Symbol(""),xr=Symbol(""),Ps=Symbol(""),zi=Symbol(""),os=Symbol("");function Ft(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function nt(e,t,n,s,r,i=o=>o()){const o=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,c)=>{const d=g=>{g===!1?c(At(4,{from:n,to:t})):g instanceof Error?c(g):Jc(g)?c(At(2,{from:t,to:g})):(o&&s.enterCallbacks[r]===o&&typeof g=="function"&&o.push(g),l())},a=i(()=>e.call(s&&s.instances[r],t,n,d));let h=Promise.resolve(a);e.length<3&&(h=h.then(d)),h.catch(g=>c(g))})}function qn(e,t,n,s,r=i=>i()){const i=[];for(const o of e)for(const l in o.components){let c=o.components[l];if(!(t!=="beforeRouteEnter"&&!o.instances[l]))if(Di(c)){const a=(c.__vccOpts||c)[t];a&&i.push(nt(a,n,s,o,l,r))}else{let d=c();i.push(()=>d.then(a=>{if(!a)throw new Error("Couldn't resolve component \"".concat(l,'" at "').concat(o.path,'"'));const h=xc(a)?a.default:a;o.mods[l]=a,o.components[l]=h;const m=(h.__vccOpts||h)[t];return m&&nt(m,n,s,o,l,r)()}))}}return i}function wr(e){const t=qe(Ps),n=qe(zi),s=Se(()=>{const c=wt(e.to);return t.resolve(c)}),r=Se(()=>{const{matched:c}=s.value,{length:d}=c,a=c[d-1],h=n.matched;if(!a||!h.length)return-1;const g=h.findIndex(Pt.bind(null,a));if(g>-1)return g;const m=Sr(c[d-2]);return d>1&&Sr(a)===m&&h[h.length-1].path!==m?h.findIndex(Pt.bind(null,c[d-2])):g}),i=Se(()=>r.value>-1&&vf(n.params,s.value.params)),o=Se(()=>r.value>-1&&r.value===n.matched.length-1&&Bi(n.params,s.value.params));function l(c={}){if(yf(c)){const d=t[wt(e.replace)?"replace":"push"](wt(e.to)).catch(Bt);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:s,href:Se(()=>s.value.href),isActive:i,isExactActive:o,navigate:l}}function gf(e){return e.length===1?e[0]:e}const mf=ri({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:wr,setup(e,{slots:t}){const n=An(wr(e)),{options:s}=qe(Ps),r=Se(()=>({[Er(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Er(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const i=t.default&&gf(t.default(n));return e.custom?i:Ni("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},i)}}}),_f=mf;function yf(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function vf(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!Re(r)||r.length!==s.length||s.some((i,o)=>i!==r[o]))return!1}return!0}function Sr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Er=(e,t,n)=>e!=null?e:t!=null?t:n,bf=ri({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=qe(os),r=Se(()=>e.route||s.value),i=qe(xr,0),o=Se(()=>{let d=wt(i);const{matched:a}=r.value;let h;for(;(h=a[d])&&!h.components;)d++;return d}),l=Se(()=>r.value.matched[o.value]);on(xr,Se(()=>o.value+1)),on(pf,l),on(os,r);const c=Yr();return ln(()=>[c.value,l.value,e.name],([d,a,h],[g,m,A])=>{a&&(a.instances[h]=d,m&&m!==a&&d&&d===g&&(a.leaveGuards.size||(a.leaveGuards=m.leaveGuards),a.updateGuards.size||(a.updateGuards=m.updateGuards))),d&&a&&(!m||!Pt(a,m)||!g)&&(a.enterCallbacks[h]||[]).forEach(O=>O(d))},{flush:"post"}),()=>{const d=r.value,a=e.name,h=l.value,g=h&&h.components[a];if(!g)return Rr(n.default,{Component:g,route:d});const m=h.props[a],A=m?m===!0?d.params:typeof m=="function"?m(d):m:null,K=Ni(g,U({},A,t,{onVnodeUnmounted:j=>{j.component.isUnmounted&&(h.instances[a]=null)},ref:c}));return Rr(n.default,{Component:K,route:d})||K}}});function Rr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const xf=bf;function Hf(e){const t=lf(e.routes,e),n=e.parseQuery||hf,s=e.stringifyQuery||br,r=e.history,i=Ft(),o=Ft(),l=Ft(),c=To(Ze);let d=Ze;yt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=kn.bind(null,y=>""+y),h=kn.bind(null,Nc),g=kn.bind(null,Qt);function m(y,P){let R,M;return ki(y)?(R=t.getRecordMatcher(y),M=P):M=y,t.addRoute(M,R)}function A(y){const P=t.getRecordMatcher(y);P&&t.removeRoute(P)}function O(){return t.getRoutes().map(y=>y.record)}function K(y){return!!t.getRecordMatcher(y)}function j(y,P){if(P=U({},P||c.value),typeof y=="string"){const p=Wn(n,y,P.path),_=t.resolve({path:p.path},P),b=r.createHref(p.fullPath);return U(p,_,{params:g(_.params),hash:Qt(p.hash),redirectedFrom:void 0,href:b})}let R;if(y.path!=null)R=U({},y,{path:Wn(n,y.path,P.path).path});else{const p=U({},y.params);for(const _ in p)p[_]==null&&delete p[_];R=U({},y,{params:h(p)}),P.params=h(P.params)}const M=t.resolve(R,P),Q=y.hash||"";M.params=a(g(M.params));const f=Hc(s,U({},y,{hash:Mc(Q),path:M.path})),u=r.createHref(f);return U({fullPath:f,hash:Q,query:s===br?df(y.query):y.query||{}},M,{redirectedFrom:void 0,href:u})}function I(y){return typeof y=="string"?Wn(n,y,c.value.path):U({},y)}function D(y,P){if(d!==y)return At(8,{from:P,to:y})}function T(y){return ee(y)}function z(y){return T(U(I(y),{replace:!0}))}function se(y){const P=y.matched[y.matched.length-1];if(P&&P.redirect){const{redirect:R}=P;let M=typeof R=="function"?R(y):R;return typeof M=="string"&&(M=M.includes("?")||M.includes("#")?M=I(M):{path:M},M.params={}),U({query:y.query,hash:y.hash,params:M.path!=null?{}:y.params},M)}}function ee(y,P){const R=d=j(y),M=c.value,Q=y.state,f=y.force,u=y.replace===!0,p=se(R);if(p)return ee(U(I(p),{state:typeof p=="object"?U({},Q,p.state):Q,force:f,replace:u}),P||R);const _=R;_.redirectedFrom=P;let b;return!f&&$c(s,M,R)&&(b=At(16,{to:_,from:M}),Oe(M,M,!0,!1)),(b?Promise.resolve(b):Pe(_,M)).catch(v=>Ve(v)?Ve(v,2)?v:Xe(v):B(v,_,M)).then(v=>{if(v){if(Ve(v,2))return ee(U({replace:u},I(v.to),{state:typeof v.to=="object"?U({},Q,v.to.state):Q,force:f}),P||_)}else v=lt(_,M,!0,u,Q);return Je(_,M,v),v})}function Ce(y,P){const R=D(y,P);return R?Promise.reject(R):Promise.resolve()}function Ye(y){const P=gt.values().next().value;return P&&typeof P.runWithContext=="function"?P.runWithContext(y):y()}function Pe(y,P){let R;const[M,Q,f]=wf(y,P);R=qn(M.reverse(),"beforeRouteLeave",y,P);for(const p of M)p.leaveGuards.forEach(_=>{R.push(nt(_,y,P))});const u=Ce.bind(null,y,P);return R.push(u),be(R).then(()=>{R=[];for(const p of i.list())R.push(nt(p,y,P));return R.push(u),be(R)}).then(()=>{R=qn(Q,"beforeRouteUpdate",y,P);for(const p of Q)p.updateGuards.forEach(_=>{R.push(nt(_,y,P))});return R.push(u),be(R)}).then(()=>{R=[];for(const p of f)if(p.beforeEnter)if(Re(p.beforeEnter))for(const _ of p.beforeEnter)R.push(nt(_,y,P));else R.push(nt(p.beforeEnter,y,P));return R.push(u),be(R)}).then(()=>(y.matched.forEach(p=>p.enterCallbacks={}),R=qn(f,"beforeRouteEnter",y,P,Ye),R.push(u),be(R))).then(()=>{R=[];for(const p of o.list())R.push(nt(p,y,P));return R.push(u),be(R)}).catch(p=>Ve(p,8)?p:Promise.reject(p))}function Je(y,P,R){l.list().forEach(M=>Ye(()=>M(y,P,R)))}function lt(y,P,R,M,Q){const f=D(y,P);if(f)return f;const u=P===Ze,p=yt?history.state:{};R&&(M||u?r.replace(y.fullPath,U({scroll:u&&p&&p.scroll},Q)):r.push(y.fullPath,Q)),c.value=y,Oe(y,P,R,u),Xe()}let Ae;function Ot(){Ae||(Ae=r.listen((y,P,R)=>{if(!en.listening)return;const M=j(y),Q=se(M);if(Q){ee(U(Q,{replace:!0,force:!0}),M).catch(Bt);return}d=M;const f=c.value;yt&&qc(ar(f.fullPath,R.delta),Nn()),Pe(M,f).catch(u=>Ve(u,12)?u:Ve(u,2)?(ee(U(I(u.to),{force:!0}),M).then(p=>{Ve(p,20)&&!R.delta&&R.type===Yt.pop&&r.go(-1,!1)}).catch(Bt),Promise.reject()):(R.delta&&r.go(-R.delta,!1),B(u,M,f))).then(u=>{u=u||lt(M,f,!1),u&&(R.delta&&!Ve(u,8)?r.go(-R.delta,!1):R.type===Yt.pop&&Ve(u,20)&&r.go(-1,!1)),Je(M,f,u)}).catch(Bt)}))}let dt=Ft(),ne=Ft(),G;function B(y,P,R){Xe(y);const M=ne.list();return M.length?M.forEach(Q=>Q(y,P,R)):console.error(y),Promise.reject(y)}function Le(){return G&&c.value!==Ze?Promise.resolve():new Promise((y,P)=>{dt.add([y,P])})}function Xe(y){return G||(G=!y,Ot(),dt.list().forEach(([P,R])=>y?R(y):P()),dt.reset()),y}function Oe(y,P,R,M){const{scrollBehavior:Q}=e;if(!yt||!Q)return Promise.resolve();const f=!R&&Gc(ar(y.fullPath,0))||(M||!R)&&history.state&&history.state.scroll||null;return bs().then(()=>Q(y,P,f)).then(u=>u&&Wc(u)).catch(u=>B(u,y,P))}const ae=y=>r.go(y);let pt;const gt=new Set,en={currentRoute:c,listening:!0,addRoute:m,removeRoute:A,clearRoutes:t.clearRoutes,hasRoute:K,getRoutes:O,resolve:j,options:e,push:T,replace:z,go:ae,back:()=>ae(-1),forward:()=>ae(1),beforeEach:i.add,beforeResolve:o.add,afterEach:l.add,onError:ne.add,isReady:Le,install(y){const P=this;y.component("RouterLink",_f),y.component("RouterView",xf),y.config.globalProperties.$router=P,Object.defineProperty(y.config.globalProperties,"$route",{enumerable:!0,get:()=>wt(c)}),yt&&!pt&&c.value===Ze&&(pt=!0,T(r.location).catch(Q=>{}));const R={};for(const Q in Ze)Object.defineProperty(R,Q,{get:()=>c.value[Q],enumerable:!0});y.provide(Ps,P),y.provide(zi,Gr(R)),y.provide(os,c);const M=y.unmount;gt.add(y),y.unmount=function(){gt.delete(y),gt.size<1&&(d=Ze,Ae&&Ae(),Ae=null,c.value=Ze,pt=!1,G=!1),M()}}};function be(y){return y.reduce((P,R)=>P.then(()=>Ye(R)),Promise.resolve())}return en}function wf(e,t){const n=[],s=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let o=0;o<i;o++){const l=t.matched[o];l&&(e.matched.find(d=>Pt(d,l))?s.push(l):n.push(l));const c=e.matched[o];c&&(t.matched.find(d=>Pt(d,c))||r.push(c))}return[n,s,r]}export{Ue as F,Mi as a,ge as b,Pf as c,ri as d,Ml as e,Rf as f,Hf as g,Df as h,Nf as i,jf as j,Of as k,If as l,Sf as m,hs as n,Ol as o,Cf as p,Nl as q,Ef as r,Mf as s,co as t,Ff as u,Tf as v,Ko as w,Af as x,as as y};
