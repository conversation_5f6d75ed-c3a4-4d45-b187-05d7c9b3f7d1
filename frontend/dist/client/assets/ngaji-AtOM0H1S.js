import{c as u,a as s,k as g,n as f,t as p,l as c,m as l,v as d,F as w,p as y,q as m,o as h}from"./vendor-Xnl27S3x.js";import{_ as S}from"./index-DPvvX1pu.js";Array.prototype.flatMap||(Array.prototype.flatMap=function(o){return Array.prototype.concat.apply([],this.map(o))});function D(o={}){const e={success:!0,purged:[]};try{if(o.localStorage&&(localStorage.clear(),e.purged.push("localStorage")),o.sessionStorage&&(sessionStorage.clear(),e.purged.push("sessionStorage")),o.specificKeys&&Array.isArray(o.specificKeys))for(const i of o.specificKeys)localStorage.removeItem(i),sessionStorage.removeItem(i),e.purged.push("key:".concat(i));return o.memoryCache&&e.purged.push("memoryCache"),e}catch(i){return{success:!1,error:i.message}}}const v={data(){return{formData:{nama:"",ranah:"",detail_ranah:"",jam_hadir:"",tanggal:"",acara:"",lokasi:""},showSuccess:!1,kelompokInput:"",previousKelompokInput:"",showSuggestions:!1,kelompokOptions:{},isLoading:!0,loadError:null,dataLoaded:!1,placeholderText:"---",displayAcara:"",isMobileKeyboardVisible:!1,inputTimer:null,isComposing:!1}},computed:{flattenedKelompok(){return Object.entries(this.kelompokOptions).flatMap(([o,e])=>e.map(i=>({desa:o,kelompok:i})))},filteredKelompok(){const o=this.kelompokInput.toLowerCase();if(console.log('Computing filteredKelompok with search term: "'.concat(o,'"')),!o||o.length<1)return console.log("Search term too short or empty, returning empty results"),[];if(!this.dataLoaded)return console.log("Data not yet loaded, returning empty array"),[];console.log("Finding matches in ".concat(this.flattenedKelompok.length," total options"));const e=this.flattenedKelompok.filter(n=>n.kelompok.toLowerCase().includes(o)||n.desa.toLowerCase().includes(o));console.log("Found ".concat(e.length,' initial matches for "').concat(o,'"'));const i=[],r=new Set;for(const n of e){const a="".concat(n.kelompok.toLowerCase(),"-").concat(n.desa.toLowerCase());r.has(a)||(r.add(a),i.push(n))}return console.log("Returned ".concat(i.length," unique matches for suggestions")),i}},watch:{kelompokInput(o){console.log('kelompokInput changed to "'.concat(o,'" (length: ').concat(o.length,")"));const e=o.length>=1;console.log("Setting showSuggestions to ".concat(e," based on input length")),this.showSuggestions=e}},methods:{formatDate(o){const e=["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"],i=["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"],r=new Date("".concat(o,"T00:00:00")),n=new Date(r.toLocaleString("en-US",{timeZone:"Asia/Jakarta"}));return"".concat(e[n.getDay()],", ").concat(n.getDate()," ").concat(i[n.getMonth()]," ").concat(n.getFullYear())},getUrlParameter(o){return new URLSearchParams(window.location.search).get(o)||""},handleKelompokInput(o){console.log('handleKelompokInput called with current input: "'.concat(this.kelompokInput,'"')),this.inputTimer&&clearTimeout(this.inputTimer),this.inputTimer=setTimeout(()=>{const e=this.$refs.kelompokInputEl;!this.kelompokInput&&e&&e.value&&(console.log('Synchronizing input value: "'.concat(e.value,'"')),this.kelompokInput=e.value),this.kelompokInput.length<this.previousKelompokInput.length&&(console.log("Deletion detected, clearing input"),this.kelompokInput=""),this.previousKelompokInput=this.kelompokInput;const i=this.kelompokInput.length>=1;console.log("Setting showSuggestions to ".concat(i," based on input length (").concat(this.kelompokInput.length,")")),this.showSuggestions=i,(!this.kelompokInput.includes(" (")||!this.kelompokInput.includes(")"))&&(console.log("New input detected, clearing previous selection"),this.formData.detail_ranah="",this.formData.ranah=""),!this.dataLoaded&&!this.isLoading&&(console.log("Data not loaded, retrying fetch..."),this.fetchKelompokData())},50)},handleKelompokFocus(){console.log('handleKelompokFocus called with current input: "'.concat(this.kelompokInput,'"'));const o=this.kelompokInput.length>=1;console.log("Focus event: Setting showSuggestions to ".concat(o," (input length: ").concat(this.kelompokInput.length,")")),this.showSuggestions=o},handleKelompokBlur(){console.log("handleKelompokBlur called, scheduling suggestion hide"),this._selectionInProgress||setTimeout(()=>{console.log("Blur timeout executed, hiding suggestions"),this.showSuggestions=!1},150)},selectKelompok(o){this._selectionInProgress=!0,console.log("selectKelompok called with item: ".concat(o.kelompok," (").concat(o.desa,")")),this.kelompokInput="".concat(o.kelompok," (").concat(o.desa,")"),this.formData.detail_ranah=o.kelompok,this.formData.ranah=o.desa,this.formData={...this.formData},console.log("Form data updated immediately:",{detail_ranah:this.formData.detail_ranah,ranah:this.formData.ranah}),setTimeout(()=>{this.showSuggestions=!1,this._selectionInProgress=!1,console.log("Verification after timeout:",{detail_ranah:this.formData.detail_ranah,ranah:this.formData.ranah})},200)},validateForm(){return this.formData.nama.trim()?!this.formData.detail_ranah||!this.formData.ranah?(alert("Kelompok harus dipilih dari daftar yang tersedia"),!1):this.flattenedKelompok.some(e=>e.kelompok.toLowerCase()===this.formData.detail_ranah.toLowerCase()&&e.desa.toLowerCase()===this.formData.ranah.toLowerCase())?(console.log("Form validation successful ✅"),!0):(console.error("Validation failed: Invalid kelompok selection",{input:{detail_ranah:this.formData.detail_ranah,ranah:this.formData.ranah},availableOptions:this.flattenedKelompok}),alert("Silahkan pilih kelompok sesuai dengan pilihan yang muncul saat Anda mengetik"),!1):(alert("Nama harus diisi"),!1)},async submitForm(){if(console.group("Form Submission Process"),console.log("Starting form submission..."),console.log("Form data before validation:",JSON.stringify(this.formData)),!this.validateForm()){console.warn("Form validation failed, submission aborted"),console.groupEnd();return}console.log("Generating detailed timestamp using Asia/Jakarta timezone");const o=new Date;console.log("Original Date object:",o),console.log("Browser timezone:",Intl.DateTimeFormat().resolvedOptions().timeZone);const e=new Date(o.toLocaleString("en-US",{timeZone:"Asia/Jakarta"}));console.log("Date conversion details:",{originalTimestamp:o.toISOString(),utcTime:o.toUTCString(),convertedTimestamp:e.toISOString(),convertedUTC:e.toUTCString(),rawLocaleString:o.toLocaleString("en-US",{timeZone:"Asia/Jakarta"})}),this.formData.jam_hadir="".concat(e.getHours().toString().padStart(2,"0"),":").concat(e.getMinutes().toString().padStart(2,"0")),this.formData.tanggal=e.toLocaleDateString("en-CA",{timeZone:"Asia/Jakarta"}),console.log("Generated timestamp details:",{jam_hadir:this.formData.jam_hadir,tanggal:this.formData.tanggal,hours:e.getHours(),minutes:e.getMinutes(),day:e.getDate(),month:e.getMonth()+1,year:e.getFullYear(),rawDate:e.toString()});const i=this.getUrlParameter("key");if(console.log("API Key detection:",{present:!!i,keyLength:i?i.length:0,firstFourChars:i?"".concat(i.substring(0,4),"..."):"none"}),!i){console.error("API key not found in URL parameters"),console.log("Full URL:",window.location.href),console.log("URL params:",new URLSearchParams(window.location.search).toString()),alert("Kunci API tidak ditemukan. Silakan berikan kunci yang valid di URL"),console.groupEnd();return}try{console.log("Preparing form payload for submission..."),console.log("Original form data:",JSON.stringify(this.formData,null,2)),console.log("Input field focus history:",this._debugInputHistory||"Not tracked"),console.log("Last focused element:",document.activeElement?document.activeElement.id||document.activeElement.tagName:"None");const r={...this.formData,nama:this.formData.nama.trim(),detail_ranah:this.formData.detail_ranah.trim(),ranah:this.formData.ranah.trim()};if(console.log("Processed payload:",JSON.stringify(r,null,2)),console.log("Browser details:",{userAgent:navigator.userAgent,language:navigator.language,cookiesEnabled:navigator.cookieEnabled,onLine:navigator.onLine,screenSize:"".concat(window.screen.width,"x").concat(window.screen.height),viewport:"".concat(window.innerWidth,"x").concat(window.innerHeight)}),console.log("Sending JSON data to API endpoint..."),!navigator.onLine)throw new Error("Tidak ada koneksi internet. Mohon periksa koneksi Anda dan coba lagi.");const n=await fetch("/api/absen-pengajian/",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"ApiKey ".concat(i)},body:JSON.stringify(r)});let a;try{const t=await n.text();try{a=JSON.parse(t)}catch(k){throw console.error("Failed to parse response:",t),new Error("Respons server tidak valid. Silakan coba lagi nanti.")}}catch(t){console.error("Failed to parse response as JSON:",t),alert("Terjadi kesalahan saat memproses respons dari server. Silakan coba lagi nanti.");return}if(n.status===422){const t=a.detail?Array.isArray(a.detail)?a.detail.join("\n"):a.detail:"Data yang dikirim tidak valid";alert(t);return}if(n.ok&&a.id)this.showSuccess=!0;else if(a.detail){const t=Array.isArray(a.detail)?a.detail[0]:a.detail;typeof t=="string"&&t.includes("Duplicate entry detected")?alert("Data yang sama sudah dimasukkan, silakan cek kembali"):(console.error("Server error detail:",t),alert("Error: ".concat(t)))}else throw new Error("Server tidak merespon dengan benar. Silakan coba beberapa saat lagi.")}catch(r){console.error("Network or system error:",r),alert("Gagal mengirim data: ".concat(r.message||"Silakan periksa koneksi internet Anda dan coba lagi"))}finally{console.groupEnd()}},resetForm(){window.location.reload()},async fetchKelompokData(){console.log("Fetching kelompok data...");try{const o=this.getUrlParameter("data");if(!o)throw new Error("Parameter data tidak ditemukan di URL");const e=encodeURIComponent(o),i=await fetch("/api/data/daerah/".concat(e,"/"));if(!i.ok)throw new Error("Network response was not ok");const r=await i.json(),n={};for(const a of r)n[a.ranah]||(n[a.ranah]=[]),n[a.ranah].push(a.detail_ranah);this.kelompokOptions=n,this.isLoading=!1,this.loadError=null,this.dataLoaded=!0}catch(o){console.error("Error fetching kelompok data:",o),this.loadError="Gagal memuat data kelompok. Silakan muat ulang halaman.",this.isLoading=!1,this.dataLoaded=!1,setTimeout(()=>{this.fetchKelompokData()},5e3)}},processDisplayText(o){return o.replace(/[ ]{2}|\n/g,"<br>")},handleKelompokKeyup(o){const e=this.$refs.kelompokInputEl;e&&this.kelompokInput!==e.value&&(console.log('Keyup detected value mismatch. v-model: "'.concat(this.kelompokInput,'", element: "').concat(e.value,'"')),this.kelompokInput=e.value,this.handleKelompokInput())},handleCompositionEnd(o){this.isComposing=!1,console.log('Composition ended with text: "'.concat(o.data,'"')),this.kelompokInput=o.target.value,this.handleKelompokInput()}},async mounted(){console.log("Component mounted"),window.addEventListener("error",a=>{var t;(t=a.filename)!=null&&t.includes("ngaji.brkh.work")&&(console.warn("Blocked script loading error:",a.filename),a.preventDefault())},!0);try{const a=D({localStorage:!1,sessionStorage:!0,memoryCache:!0,specificKeys:["ngajiFormData","ngajiLastSubmission"]});console.log("Cache purge result:",a)}catch(a){console.warn("Cache purging failed:",a)}const o=window.visualViewport;o&&o.addEventListener("resize",()=>{const a=this.isMobileKeyboardVisible;this.isMobileKeyboardVisible=o.height<window.innerHeight*.8,console.log("Viewport resize: keyboard visible changed from ".concat(a," to ").concat(this.isMobileKeyboardVisible)),console.log("Viewport height: ".concat(o.height,", Window height: ").concat(window.innerHeight)),this.isMobileKeyboardVisible&&this.kelompokInput?(console.log("Keyboard detected AND input has value, showing suggestions"),this.showSuggestions=!0):!this.isMobileKeyboardVisible&&a&&console.log("Keyboard hidden, suggestion visibility unchanged")});try{await fetch(window.location.pathname,{cache:"reload",credentials:"same-origin"})}catch(a){console.warn("Cache clearing failed:",a)}const e=new Date,i=new Date(e.toLocaleString("en-US",{timeZone:"Asia/Jakarta"}));this.formData.tanggal=i.toLocaleDateString("en-CA",{timeZone:"Asia/Jakarta"});const r=this.getUrlParameter("acara");this.formData.acara=r,this.displayAcara=this.processDisplayText(r),this.formData.lokasi=this.getUrlParameter("lokasi");const n=this.getUrlParameter("ph");n&&(this.placeholderText=n),document.title="Absensi Acara - ".concat(r.replace(/\s{2,}/g," - ")),await this.fetchKelompokData(),document.addEventListener("touchend",a=>{a.target===this.$refs.kelompokInputEl&&setTimeout(()=>{const t=this.$refs.kelompokInputEl;t&&this.kelompokInput!==t.value&&(console.log('Touch event detected value mismatch. v-model: "'.concat(this.kelompokInput,'", element: "').concat(t.value,'"')),this.kelompokInput=t.value,this.handleKelompokInput())},100)})}},b={class:"page-wrapper"},K={class:"content-area"},I=["innerHTML"],A={class:"form-date"},L={class:"suggestions-container"},C=["placeholder"],T={class:"suggestions-wrapper"},E={key:0,class:"suggestions"},F=["onClick"],U={key:0,class:"form-container"};function M(o,e,i,r,n,a){return h(),u("div",b,[s("div",K,[s("div",{class:f(["form-container",{hidden:n.showSuccess}])},[s("div",{class:"event-title",innerHTML:n.displayAcara||"UMUM"},null,8,I),e[15]||(e[15]=s("div",{class:"form-title"},"ABSENSI",-1)),s("div",A,p(a.formatDate(n.formData.tanggal)),1),s("form",{onSubmit:e[12]||(e[12]=c((...t)=>a.submitForm&&a.submitForm(...t),["prevent"]))},[l(s("input",{type:"text","onUpdate:modelValue":e[0]||(e[0]=t=>n.formData.nama=t),placeholder:"NAMA",required:""},null,512),[[d,n.formData.nama]]),s("div",L,[l(s("input",{type:"text","onUpdate:modelValue":e[1]||(e[1]=t=>n.kelompokInput=t),onInput:e[2]||(e[2]=(...t)=>a.handleKelompokInput&&a.handleKelompokInput(...t)),onKeyup:e[3]||(e[3]=(...t)=>a.handleKelompokKeyup&&a.handleKelompokKeyup(...t)),onCompositionend:e[4]||(e[4]=(...t)=>a.handleCompositionEnd&&a.handleCompositionEnd(...t)),onFocus:e[5]||(e[5]=(...t)=>a.handleKelompokFocus&&a.handleKelompokFocus(...t)),onBlur:e[6]||(e[6]=(...t)=>a.handleKelompokBlur&&a.handleKelompokBlur(...t)),ref:"kelompokInputEl",placeholder:n.placeholderText,required:""},null,40,C),[[d,n.kelompokInput]]),s("div",T,[n.showSuggestions&&n.kelompokInput&&a.filteredKelompok.length?(h(),u("div",E,[(h(!0),u(w,null,y(a.filteredKelompok,t=>(h(),u("div",{key:"".concat(t.kelompok,"-").concat(t.desa),class:"suggestion-item",onClick:c(k=>a.selectKelompok(t),["stop"])},p(t.kelompok)+" ("+p(t.desa)+") ",9,F))),128))])):g("",!0)])]),l(s("input",{type:"hidden","onUpdate:modelValue":e[7]||(e[7]=t=>n.formData.detail_ranah=t)},null,512),[[d,n.formData.detail_ranah]]),l(s("input",{type:"hidden","onUpdate:modelValue":e[8]||(e[8]=t=>n.formData.ranah=t)},null,512),[[d,n.formData.ranah]]),l(s("input",{type:"hidden","onUpdate:modelValue":e[9]||(e[9]=t=>n.formData.jam_hadir=t)},null,512),[[d,n.formData.jam_hadir]]),l(s("input",{type:"hidden","onUpdate:modelValue":e[10]||(e[10]=t=>n.formData.lokasi=t)},null,512),[[d,n.formData.lokasi]]),l(s("input",{type:"hidden","onUpdate:modelValue":e[11]||(e[11]=t=>n.formData.tanggal=t)},null,512),[[d,n.formData.tanggal]]),e[14]||(e[14]=s("button",{type:"submit"},"Kirim Data",-1))],32)],2),n.showSuccess?(h(),u("div",U,[e[16]||(e[16]=s("div",{class:"confirmation-message"},[m(" DATA ABSEN ANDA"),s("br"),m("SUDAH KAMI TERIMA."),s("br"),s("br"),m("Alhamdulillah"),s("br"),m("Jazaa Kumullohu Khoiro. ")],-1)),s("button",{onClick:e[13]||(e[13]=(...t)=>a.resetForm&&a.resetForm(...t))},"Kembali")])):g("",!0)]),e[17]||(e[17]=s("div",{class:"footer-area"},[s("div",{class:"warning-container"},[m(" WARNING!!!"),s("br"),m("DILARANG mengoperasikan HP"),s("br"),m("selama acara berlangsung. ")])],-1))])}const V=S(v,[["render",M]]);export{V as default};
