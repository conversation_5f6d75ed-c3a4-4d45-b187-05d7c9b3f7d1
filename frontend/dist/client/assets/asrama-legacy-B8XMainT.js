System.register(["./vendor-legacy-BmDVBcfe.js","./index-legacy-B2X8pP_9.js"],function(e,t){"use strict";var o,a,n,i,r,s,l,d,p,m,c,h,u,g;return{setters:[e=>{o=e.c,a=e.a,n=e.k,i=e.n,r=e.t,s=e.l,l=e.m,d=e.s,p=e.F,m=e.p,c=e.v,h=e.q,u=e.o},e=>{g=e._}],execute:function(){var t=document.createElement("style");t.textContent="body{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif;margin:0;padding:0;background-color:#fff;color:#2c4a3e;display:flex;justify-content:center;align-items:center;height:100vh}.form-container{width:100%;max-width:360px;padding:20px;border-radius:20px;background-color:#f9f9f9;box-shadow:0 10px 25px rgba(44,74,62,.2),0 6px 12px rgba(44,74,62,.15);box-sizing:border-box;text-align:center;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transition:opacity .3s ease-in-out}.hidden{opacity:0;pointer-events:none}.form-title{font-size:24px;font-weight:700;color:#2e5a35;margin-bottom:12px}input,select{width:100%;padding:12px;margin-bottom:15px;border:1px solid #2e5a35;border-radius:20px;font-size:16px;transition:border-color .3s ease;box-sizing:border-box;-webkit-appearance:none;appearance:none;background-color:#fff;color:#2c4a3e}select{padding-right:40px;background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%232e5a35' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\");background-repeat:no-repeat;background-position:right 12px center;background-size:16px;cursor:pointer;text-align:left}select:disabled{background-color:#f0f0f0;cursor:not-allowed;opacity:.7}input::placeholder{color:#2e5a35;opacity:.7}input:focus,select:focus{border-color:#2e5a35;outline:none}button{width:100%;padding:12px;border:none;border-radius:20px;background-color:#2e5a35;color:#fff;font-size:16px;cursor:pointer;transition:background-color .3s ease;box-sizing:border-box;margin-top:10px}button:hover{background-color:#3d7a47}.confirmation-message{font-size:20px;font-weight:700;color:#2e5a35}.suggestions{position:relative;width:100%;max-height:40vh;overflow-y:auto;background:#fff;border:1px solid #2e5a35;border-radius:10px;box-shadow:0 4px 12px rgba(0,0,0,.5);margin-top:5px;-webkit-overflow-scrolling:touch;overscroll-behavior:contain;z-index:10000!important}.suggestion-item{padding:10px;cursor:pointer;text-align:left;color:#2c4a3e;touch-action:pan-y}.suggestion-item{padding:10px;cursor:pointer;text-align:left;color:#2c4a3e}.suggestion-item:hover{background-color:#e8f3ed}.event-title{margin-bottom:8px;font-weight:700;color:#2e5a35;font-size:18px;white-space:pre-line}.event-date{margin-bottom:15px;color:#2e5a35;font-size:14px}.suggestions-container{position:relative;z-index:10000!important;margin-bottom:25px}.suggestions-wrapper{position:absolute;top:100%;left:0;width:100%;z-index:10000!important}.warning-container{width:100%;max-width:360px;padding:15px;border-radius:10px;box-shadow:0 4px 8px rgba(44,74,62,.1);box-sizing:border-box;text-align:center;position:fixed;bottom:20px;left:50%;transform:translate(-50%);font-weight:700;animation:blinkColors 1s infinite;z-index:100}@keyframes blinkColors{0%,to{background-color:#c00;color:#fff}50%{background-color:#fff;color:#c00}}.page-wrapper{display:flex;flex-direction:column;min-height:100vh;position:relative;padding-bottom:0;width:100%}.content-area{flex:1;position:relative}.footer-area{position:fixed;width:100%;margin-top:auto;padding-bottom:5px;bottom:0}.form-container{width:100%;max-width:360px;padding:20px;border-radius:20px;background-color:#f9f9f9;box-shadow:0 10px 25px rgba(44,74,62,.2),0 6px 12px rgba(44,74,62,.15);box-sizing:border-box;text-align:center;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transition:opacity .3s ease-in-out;z-index:100}.warning-container{width:100%;max-width:360px;padding:15px;margin:0 auto;border-radius:10px;background-color:#c00;color:#fff;box-shadow:0 4px 8px rgba(44,74,62,.1);box-sizing:border-box;text-align:center;font-weight:700;animation:blinkColors 1s infinite;z-index:50;line-height:1.25}\n/*$vite$:1*/",document.head.appendChild(t);const k={class:"page-wrapper"},f={class:"content-area"},b=["innerHTML"],x={key:0,class:"event-date"},w=["disabled"],y=["value"],v={class:"suggestions-container"},D=["placeholder"],S={class:"suggestions-wrapper"},I={key:0,class:"suggestions"},K=["onClick"],A={key:0,class:"form-container"};e("default",g({data:()=>({formData:{nama:"",ranah:"",detail_ranah:"",jam_hadir:"",tanggal:"",acara:"",lokasi:"",sesi:""},sesiOptions:[],showSuccess:!1,kelompokInput:"",previousKelompokInput:"",showSuggestions:!1,kelompokOptions:{},isLoading:!0,loadError:null,dataLoaded:!1,placeholderText:"KELOMPOK (DESA) / DAPUKAN",displayAcara:"",isMobileKeyboardVisible:!1,inputTimer:null,isComposing:!1}),computed:{flattenedKelompok(){return Object.entries(this.kelompokOptions).flatMap(([e,t])=>t.map(t=>({desa:e,kelompok:t})))},filteredKelompok(){const e=this.kelompokInput.toLowerCase();if(!e||e.length<1)return[];if(!this.dataLoaded)return console.log("Data not yet loaded, returning empty array"),[];const t=this.flattenedKelompok.filter(t=>t.kelompok.toLowerCase().includes(e)||t.desa.toLowerCase().includes(e)),o=[],a=new Set;for(const n of t){const e=`${n.kelompok.toLowerCase()}-${n.desa.toLowerCase()}`;a.has(e)||(a.add(e),o.push(n))}return o}},watch:{kelompokInput(e){console.log(`kelompokInput changed to "${e}" (length: ${e.length})`);const t=e.length>=1;console.log(`Setting showSuggestions to ${t} based on input length`),this.showSuggestions=t}},methods:{formatDate(e){const t=new Date(`${e}T00:00:00`),o=new Date(t.toLocaleString("en-US",{timeZone:"Asia/Jakarta"}));return`${["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"][o.getDay()]}, ${o.getDate()} ${["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"][o.getMonth()]} ${o.getFullYear()}`},getUrlParameter:e=>new URLSearchParams(window.location.search).get(e)||"",handleKelompokInput(){console.log(`handleKelompokInput called with current input: "${this.kelompokInput}"`),this.inputTimer&&clearTimeout(this.inputTimer),this.inputTimer=setTimeout(()=>{const e=this.$refs.kelompokInputEl;!this.kelompokInput&&e&&e.value&&(console.log(`Synchronizing input value: "${e.value}"`),this.kelompokInput=e.value),this.kelompokInput.length<this.previousKelompokInput.length&&(console.log("Deletion detected, clearing input"),this.kelompokInput=""),this.previousKelompokInput=this.kelompokInput;const t=this.kelompokInput.length>=1;console.log(`Setting showSuggestions to ${t} based on input length (${this.kelompokInput.length})`),this.showSuggestions=t,this.kelompokInput.includes(" (")&&this.kelompokInput.includes(")")||(console.log("New input detected, clearing previous selection"),this.formData.detail_ranah="",this.formData.ranah=""),this.dataLoaded||this.isLoading||(console.log("Data not loaded, retrying fetch..."),this.fetchKelompokData())},50)},handleKelompokKeyup(e){const t=this.$refs.kelompokInputEl;t&&this.kelompokInput!==t.value&&(console.log(`Keyup detected value mismatch. v-model: "${this.kelompokInput}", element: "${t.value}"`),this.kelompokInput=t.value,this.handleKelompokInput())},handleCompositionEnd(e){this.isComposing=!1,console.log(`Composition ended with text: "${e.data}"`),this.kelompokInput=e.target.value,this.handleKelompokInput()},handleKelompokFocus(){console.log(`handleKelompokFocus called with current input: "${this.kelompokInput}"`);const e=this.kelompokInput.length>=1;console.log(`Focus event: Setting showSuggestions to ${e} (input length: ${this.kelompokInput.length})`),this.showSuggestions=e},handleKelompokBlur(){console.log("handleKelompokBlur called, scheduling suggestion hide"),this._selectionInProgress||setTimeout(()=>{console.log("Blur timeout executed, hiding suggestions"),this.showSuggestions=!1},150)},selectKelompok(e){this._selectionInProgress=!0,console.log(`selectKelompok called with item: ${e.kelompok} (${e.desa})`),this.kelompokInput=`${e.kelompok} (${e.desa})`,this.formData.detail_ranah=e.kelompok,this.formData.ranah=e.desa,this.formData={...this.formData},console.log("Form data updated immediately:",{detail_ranah:this.formData.detail_ranah,ranah:this.formData.ranah}),setTimeout(()=>{this.showSuggestions=!1,this._selectionInProgress=!1,console.log("Verification after timeout:",{detail_ranah:this.formData.detail_ranah,ranah:this.formData.ranah})},200)},validateForm(){return this.formData.nama.trim()?this.formData.sesi?this.formData.detail_ranah&&this.formData.ranah?!!this.flattenedKelompok.some(e=>e.kelompok.toLowerCase()===this.formData.detail_ranah.toLowerCase()&&e.desa.toLowerCase()===this.formData.ranah.toLowerCase())||(alert("Silahkan pilih kelompok sesuai dengan pilihan yang muncul saat Anda mengetik"),!1):(alert("Kelompok harus dipilih dari daftar yang tersedia"),!1):(alert("Sesi harus dipilih"),!1):(alert("Nama harus diisi"),!1)},async submitForm(){if(!this.validateForm())return;const e=new Date,t=new Date(e.toLocaleString("en-US",{timeZone:"Asia/Jakarta"}));this.formData.jam_hadir=`${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`,this.formData.tanggal=t.toLocaleDateString("en-CA",{timeZone:"Asia/Jakarta"});const o=this.getUrlParameter("key");if(o)try{const e={...this.formData,nama:this.formData.nama.trim(),detail_ranah:this.formData.detail_ranah.trim(),ranah:this.formData.ranah.trim()},t=await fetch("/api/absen-asramaan/",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`ApiKey ${o}`},body:JSON.stringify(e)});let n;try{const e=await t.text();n=JSON.parse(e)}catch(a){return console.error("Failed to parse response as JSON:",a),void alert("Terjadi kesalahan saat memproses respons dari server")}if(422===t.status){const e=n.detail?Array.isArray(n.detail)?n.detail.join("\n"):n.detail:"Data yang dikirim tidak valid";return void alert(e)}if(t.ok&&n.id)this.showSuccess=!0;else{if(!n.detail)throw new Error("Unexpected response format");{const e=Array.isArray(n.detail)?n.detail[0]:n.detail;"string"==typeof e&&e.includes("Duplicate entry detected")?alert("Data yang sama sudah dimasukkan, silakan cek kembali"):(console.error("Server error detail:",e),alert(`Error: ${e}`))}}}catch(n){console.error("Network or system error:",n),alert("Terjadi kesalahan saat mengirim data. Silakan coba lagi")}else alert("Kunci API tidak ditemukan. Silakan berikan kunci yang valid di URL")},resetForm(){window.location.reload()},async fetchKelompokData(){console.log("Fetching kelompok data...");try{const e=this.getUrlParameter("data");if(!e)throw new Error("Parameter data tidak ditemukan di URL");const t=encodeURIComponent(e),o=await fetch(`/api/data/daerah/${t}/`);if(!o.ok)throw new Error("Network response was not ok");const a=await o.json(),n={};for(const i of a)n[i.ranah]||(n[i.ranah]=[]),n[i.ranah].push(i.detail_ranah);this.kelompokOptions=n,this.isLoading=!1,this.loadError=null,this.dataLoaded=!0}catch(e){console.error("Error fetching kelompok data:",e),this.loadError="Gagal memuat data kelompok. Silakan muat ulang halaman.",this.isLoading=!1,this.dataLoaded=!1,setTimeout(()=>{this.fetchKelompokData()},5e3)}},async fetchSesiData(){try{const e=this.getUrlParameter("sesi");if(!e)return void console.warn("Parameter sesi tidak ditemukan di URL");const t=encodeURIComponent(e),o=await fetch(`/api/data/sesi/${t}`);if(!o.ok)throw new Error("Network response was not ok");const a=await o.json();this.sesiOptions=Array.isArray(a)?a.map(e=>e.sesi):[]}catch(e){console.error("Error fetching sesi data:",e),alert("Gagal memuat data sesi. Silakan muat ulang halaman.")}},processDisplayText:e=>e.replace(/[ ]{2}|\n/g,"<br>")},async mounted(){console.log("Component mounted");try{await fetch(window.location.pathname,{cache:"reload",credentials:"same-origin"})}catch(n){console.warn("Cache clearing failed:",n)}const e=new Date,t=new Date(e.toLocaleString("en-US",{timeZone:"Asia/Jakarta"}));this.formData.tanggal=t.toLocaleDateString("en-CA",{timeZone:"Asia/Jakarta"});const o=this.getUrlParameter("acara");this.formData.acara=o,this.displayAcara=this.processDisplayText(o),this.formData.lokasi=this.getUrlParameter("lokasi");const a=this.getUrlParameter("ph");a&&(this.placeholderText=a),document.title=`Absensi Acara - ${o.replace(/\s{2,}/g," - ")}`,await Promise.all([this.fetchKelompokData(),this.fetchSesiData()]),document.addEventListener("touchend",e=>{e.target===this.$refs.kelompokInputEl&&setTimeout(()=>{const e=this.$refs.kelompokInputEl;e&&this.kelompokInput!==e.value&&(console.log(`Touch event detected value mismatch. v-model: "${this.kelompokInput}", element: "${e.value}"`),this.kelompokInput=e.value,this.handleKelompokInput())},100)})}},[["render",function(e,t,g,$,C,L){return u(),o("div",k,[a("div",f,[a("div",{class:i(["form-container",{hidden:C.showSuccess}])},[a("div",{class:"event-title",innerHTML:C.displayAcara||"ASRAMA"},null,8,b),t[17]||(t[17]=a("div",{class:"form-title"},"ABSENSI",-1)),C.formData.tanggal?(u(),o("div",x,r(L.formatDate(C.formData.tanggal)),1)):n("",!0),a("form",{onSubmit:t[13]||(t[13]=s((...e)=>L.submitForm&&L.submitForm(...e),["prevent"]))},[l(a("select",{"onUpdate:modelValue":t[0]||(t[0]=e=>C.formData.sesi=e),required:"",disabled:!C.sesiOptions.length},[t[15]||(t[15]=a("option",{value:"",disabled:"",selected:""},"SESI",-1)),(u(!0),o(p,null,m(C.sesiOptions,e=>(u(),o("option",{key:e,value:e},r(e),9,y))),128))],8,w),[[d,C.formData.sesi]]),l(a("input",{type:"text","onUpdate:modelValue":t[1]||(t[1]=e=>C.formData.nama=e),placeholder:"NAMA",required:""},null,512),[[c,C.formData.nama]]),a("div",v,[l(a("input",{type:"text","onUpdate:modelValue":t[2]||(t[2]=e=>C.kelompokInput=e),onInput:t[3]||(t[3]=(...e)=>L.handleKelompokInput&&L.handleKelompokInput(...e)),onKeyup:t[4]||(t[4]=(...e)=>L.handleKelompokKeyup&&L.handleKelompokKeyup(...e)),onCompositionend:t[5]||(t[5]=(...e)=>L.handleCompositionEnd&&L.handleCompositionEnd(...e)),onFocus:t[6]||(t[6]=(...e)=>L.handleKelompokFocus&&L.handleKelompokFocus(...e)),onBlur:t[7]||(t[7]=(...e)=>L.handleKelompokBlur&&L.handleKelompokBlur(...e)),ref:"kelompokInputEl",style:{"touch-action":"manipulation"},placeholder:C.placeholderText,required:""},null,40,D),[[c,C.kelompokInput]]),a("div",S,[C.showSuggestions&&C.kelompokInput&&L.filteredKelompok.length?(u(),o("div",I,[(u(!0),o(p,null,m(L.filteredKelompok,e=>(u(),o("div",{key:`${e.kelompok}-${e.desa}`,class:"suggestion-item",onClick:s(t=>L.selectKelompok(e),["stop"])},r(e.kelompok)+" ("+r(e.desa)+") ",9,K))),128))])):n("",!0)])]),l(a("input",{type:"hidden","onUpdate:modelValue":t[8]||(t[8]=e=>C.formData.detail_ranah=e)},null,512),[[c,C.formData.detail_ranah]]),l(a("input",{type:"hidden","onUpdate:modelValue":t[9]||(t[9]=e=>C.formData.ranah=e)},null,512),[[c,C.formData.ranah]]),l(a("input",{type:"hidden","onUpdate:modelValue":t[10]||(t[10]=e=>C.formData.jam_hadir=e)},null,512),[[c,C.formData.jam_hadir]]),l(a("input",{type:"hidden","onUpdate:modelValue":t[11]||(t[11]=e=>C.formData.lokasi=e)},null,512),[[c,C.formData.lokasi]]),l(a("input",{type:"hidden","onUpdate:modelValue":t[12]||(t[12]=e=>C.formData.tanggal=e)},null,512),[[c,C.formData.tanggal]]),t[16]||(t[16]=a("button",{type:"submit"},"Kirim Data",-1))],32)],2),C.showSuccess?(u(),o("div",A,[t[18]||(t[18]=a("div",{class:"confirmation-message"},[h(" DATA ABSEN ANDA"),a("br"),h("SUDAH KAMI TERIMA."),a("br"),a("br"),h("Alhamdulillah"),a("br"),h("Jazaa Kumullohu Khoiro. ")],-1)),a("button",{onClick:t[14]||(t[14]=(...e)=>L.resetForm&&L.resetForm(...e))},"Kembali")])):n("",!0)]),t[19]||(t[19]=a("div",{class:"footer-area"},[a("div",{class:"warning-container"},[h(" WARNING!!!"),a("br"),h("DILARANG mengoperasikan HP"),a("br"),h("selama acara berlangsung. ")])],-1))])}]]))}}});
