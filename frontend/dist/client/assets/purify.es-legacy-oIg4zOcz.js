System.register([],function(e,t){"use strict";return{execute:function(){/*! @license DOMPurify 3.2.6 | (c) Cure<PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:t,setPrototypeOf:n,isFrozen:o,getPrototypeOf:r,getOwnPropertyDescriptor:i}=Object;let{freeze:a,seal:l,create:c}=Object,{apply:s,construct:u}="undefined"!=typeof Reflect&&Reflect;a||(a=function(e){return e}),l||(l=function(e){return e}),s||(s=function(e,t,n){return e.apply(t,n)}),u||(u=function(e,t){return new e(...t)});const m=w(Array.prototype.forEach),p=w(Array.prototype.lastIndexOf),f=w(Array.prototype.pop),d=w(Array.prototype.push),h=w(Array.prototype.splice),g=w(String.prototype.toLowerCase),T=w(String.prototype.toString),y=w(String.prototype.match),E=w(String.prototype.replace),A=w(String.prototype.indexOf),_=w(String.prototype.trim),S=w(Object.prototype.hasOwnProperty),N=w(RegExp.prototype.test),b=(R=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return u(R,t)});var R;function w(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return s(e,t,o)}}function O(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:g;n&&n(e,null);let i=t.length;for(;i--;){let n=t[i];if("string"==typeof n){const e=r(n);e!==n&&(o(t)||(t[i]=e),n=e)}e[n]=!0}return e}function D(e){for(let t=0;t<e.length;t++)S(e,t)||(e[t]=null);return e}function L(e){const n=c(null);for(const[o,r]of t(e))S(e,o)&&(Array.isArray(r)?n[o]=D(r):r&&"object"==typeof r&&r.constructor===Object?n[o]=L(r):n[o]=r);return n}function v(e,t){for(;null!==e;){const n=i(e,t);if(n){if(n.get)return w(n.get);if("function"==typeof n.value)return w(n.value)}e=r(e)}return function(){return null}}const C=a(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),x=a(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),I=a(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),k=a(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),M=a(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),U=a(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),z=a(["#text"]),P=a(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),H=a(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),F=a(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),B=a(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),W=l(/\{\{[\w\W]*|[\w\W]*\}\}/gm),G=l(/<%[\w\W]*|[\w\W]*%>/gm),Y=l(/\$\{[\w\W]*/gm),j=l(/^data-[\-\w.\u00B7-\uFFFF]+$/),X=l(/^aria-[\-\w]+$/),q=l(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),$=l(/^(?:\w+script|data):/i),K=l(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),V=l(/^html$/i),Z=l(/^[a-z][.\w]*(-[.\w]+)+$/i);var J=Object.freeze({__proto__:null,ARIA_ATTR:X,ATTR_WHITESPACE:K,CUSTOM_ELEMENT:Z,DATA_ATTR:j,DOCTYPE_NAME:V,ERB_EXPR:G,IS_ALLOWED_URI:q,IS_SCRIPT_OR_DATA:$,MUSTACHE_EXPR:W,TMPLIT_EXPR:Y});const Q=1,ee=3,te=7,ne=8,oe=9,re=function(){return"undefined"==typeof window?null:window};e("default",function e(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:re();const o=t=>e(t);if(o.version="3.2.6",o.removed=[],!n||!n.document||n.document.nodeType!==oe||!n.Element)return o.isSupported=!1,o;let{document:r}=n;const i=r,l=i.currentScript,{DocumentFragment:s,HTMLTemplateElement:u,Node:R,Element:w,NodeFilter:D,NamedNodeMap:W=n.NamedNodeMap||n.MozNamedAttrMap,HTMLFormElement:G,DOMParser:Y,trustedTypes:j}=n,X=w.prototype,$=v(X,"cloneNode"),K=v(X,"remove"),Z=v(X,"nextSibling"),ie=v(X,"childNodes"),ae=v(X,"parentNode");if("function"==typeof u){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let le,ce="";const{implementation:se,createNodeIterator:ue,createDocumentFragment:me,getElementsByTagName:pe}=r,{importNode:fe}=i;let de={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};o.isSupported="function"==typeof t&&"function"==typeof ae&&se&&void 0!==se.createHTMLDocument;const{MUSTACHE_EXPR:he,ERB_EXPR:ge,TMPLIT_EXPR:Te,DATA_ATTR:ye,ARIA_ATTR:Ee,IS_SCRIPT_OR_DATA:Ae,ATTR_WHITESPACE:_e,CUSTOM_ELEMENT:Se}=J;let{IS_ALLOWED_URI:Ne}=J,be=null;const Re=O({},[...C,...x,...I,...M,...z]);let we=null;const Oe=O({},[...P,...H,...F,...B]);let De=Object.seal(c(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Le=null,ve=null,Ce=!0,xe=!0,Ie=!1,ke=!0,Me=!1,Ue=!0,ze=!1,Pe=!1,He=!1,Fe=!1,Be=!1,We=!1,Ge=!0,Ye=!1,je=!0,Xe=!1,qe={},$e=null;const Ke=O({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Ve=null;const Ze=O({},["audio","video","img","source","image","track"]);let Je=null;const Qe=O({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),et="http://www.w3.org/1998/Math/MathML",tt="http://www.w3.org/2000/svg",nt="http://www.w3.org/1999/xhtml";let ot=nt,rt=!1,it=null;const at=O({},[et,tt,nt],T);let lt=O({},["mi","mo","mn","ms","mtext"]),ct=O({},["annotation-xml"]);const st=O({},["title","style","font","a","script"]);let ut=null;const mt=["application/xhtml+xml","text/html"];let pt=null,ft=null;const dt=r.createElement("form"),ht=function(e){return e instanceof RegExp||e instanceof Function},gt=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!ft||ft!==e){if(e&&"object"==typeof e||(e={}),e=L(e),ut=-1===mt.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,pt="application/xhtml+xml"===ut?T:g,be=S(e,"ALLOWED_TAGS")?O({},e.ALLOWED_TAGS,pt):Re,we=S(e,"ALLOWED_ATTR")?O({},e.ALLOWED_ATTR,pt):Oe,it=S(e,"ALLOWED_NAMESPACES")?O({},e.ALLOWED_NAMESPACES,T):at,Je=S(e,"ADD_URI_SAFE_ATTR")?O(L(Qe),e.ADD_URI_SAFE_ATTR,pt):Qe,Ve=S(e,"ADD_DATA_URI_TAGS")?O(L(Ze),e.ADD_DATA_URI_TAGS,pt):Ze,$e=S(e,"FORBID_CONTENTS")?O({},e.FORBID_CONTENTS,pt):Ke,Le=S(e,"FORBID_TAGS")?O({},e.FORBID_TAGS,pt):L({}),ve=S(e,"FORBID_ATTR")?O({},e.FORBID_ATTR,pt):L({}),qe=!!S(e,"USE_PROFILES")&&e.USE_PROFILES,Ce=!1!==e.ALLOW_ARIA_ATTR,xe=!1!==e.ALLOW_DATA_ATTR,Ie=e.ALLOW_UNKNOWN_PROTOCOLS||!1,ke=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Me=e.SAFE_FOR_TEMPLATES||!1,Ue=!1!==e.SAFE_FOR_XML,ze=e.WHOLE_DOCUMENT||!1,Fe=e.RETURN_DOM||!1,Be=e.RETURN_DOM_FRAGMENT||!1,We=e.RETURN_TRUSTED_TYPE||!1,He=e.FORCE_BODY||!1,Ge=!1!==e.SANITIZE_DOM,Ye=e.SANITIZE_NAMED_PROPS||!1,je=!1!==e.KEEP_CONTENT,Xe=e.IN_PLACE||!1,Ne=e.ALLOWED_URI_REGEXP||q,ot=e.NAMESPACE||nt,lt=e.MATHML_TEXT_INTEGRATION_POINTS||lt,ct=e.HTML_INTEGRATION_POINTS||ct,De=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ht(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(De.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ht(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(De.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(De.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Me&&(xe=!1),Be&&(Fe=!0),qe&&(be=O({},z),we=[],!0===qe.html&&(O(be,C),O(we,P)),!0===qe.svg&&(O(be,x),O(we,H),O(we,B)),!0===qe.svgFilters&&(O(be,I),O(we,H),O(we,B)),!0===qe.mathMl&&(O(be,M),O(we,F),O(we,B))),e.ADD_TAGS&&(be===Re&&(be=L(be)),O(be,e.ADD_TAGS,pt)),e.ADD_ATTR&&(we===Oe&&(we=L(we)),O(we,e.ADD_ATTR,pt)),e.ADD_URI_SAFE_ATTR&&O(Je,e.ADD_URI_SAFE_ATTR,pt),e.FORBID_CONTENTS&&($e===Ke&&($e=L($e)),O($e,e.FORBID_CONTENTS,pt)),je&&(be["#text"]=!0),ze&&O(be,["html","head","body"]),be.table&&(O(be,["tbody"]),delete Le.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw b('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw b('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');le=e.TRUSTED_TYPES_POLICY,ce=le.createHTML("")}else void 0===le&&(le=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML:e=>e,createScriptURL:e=>e})}catch(i){return console.warn("TrustedTypes policy "+r+" could not be created."),null}}(j,l)),null!==le&&"string"==typeof ce&&(ce=le.createHTML(""));a&&a(e),ft=e}},Tt=O({},[...x,...I,...k]),yt=O({},[...M,...U]),Et=function(e){d(o.removed,{element:e});try{ae(e).removeChild(e)}catch(t){K(e)}},At=function(e,t){try{d(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(n){d(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(Fe||Be)try{Et(t)}catch(n){}else try{t.setAttribute(e,"")}catch(n){}},_t=function(e){let t=null,n=null;if(He)e="<remove></remove>"+e;else{const t=y(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===ut&&ot===nt&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const o=le?le.createHTML(e):e;if(ot===nt)try{t=(new Y).parseFromString(o,ut)}catch(a){}if(!t||!t.documentElement){t=se.createDocument(ot,"template",null);try{t.documentElement.innerHTML=rt?ce:o}catch(a){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(r.createTextNode(n),i.childNodes[0]||null),ot===nt?pe.call(t,ze?"html":"body")[0]:ze?t.documentElement:i},St=function(e){return ue.call(e.ownerDocument||e,e,D.SHOW_ELEMENT|D.SHOW_COMMENT|D.SHOW_TEXT|D.SHOW_PROCESSING_INSTRUCTION|D.SHOW_CDATA_SECTION,null)},Nt=function(e){return e instanceof G&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof W)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},bt=function(e){return"function"==typeof R&&e instanceof R};function Rt(e,t,n){m(e,e=>{e.call(o,t,n,ft)})}const wt=function(e){let t=null;if(Rt(de.beforeSanitizeElements,e,null),Nt(e))return Et(e),!0;const n=pt(e.nodeName);if(Rt(de.uponSanitizeElement,e,{tagName:n,allowedTags:be}),Ue&&e.hasChildNodes()&&!bt(e.firstElementChild)&&N(/<[/\w!]/g,e.innerHTML)&&N(/<[/\w!]/g,e.textContent))return Et(e),!0;if(e.nodeType===te)return Et(e),!0;if(Ue&&e.nodeType===ne&&N(/<[/\w]/g,e.data))return Et(e),!0;if(!be[n]||Le[n]){if(!Le[n]&&Dt(n)){if(De.tagNameCheck instanceof RegExp&&N(De.tagNameCheck,n))return!1;if(De.tagNameCheck instanceof Function&&De.tagNameCheck(n))return!1}if(je&&!$e[n]){const t=ae(e)||e.parentNode,n=ie(e)||e.childNodes;if(n&&t)for(let o=n.length-1;o>=0;--o){const r=$(n[o],!0);r.__removalCount=(e.__removalCount||0)+1,t.insertBefore(r,Z(e))}}return Et(e),!0}return e instanceof w&&!function(e){let t=ae(e);t&&t.tagName||(t={namespaceURI:ot,tagName:"template"});const n=g(e.tagName),o=g(t.tagName);return!!it[e.namespaceURI]&&(e.namespaceURI===tt?t.namespaceURI===nt?"svg"===n:t.namespaceURI===et?"svg"===n&&("annotation-xml"===o||lt[o]):Boolean(Tt[n]):e.namespaceURI===et?t.namespaceURI===nt?"math"===n:t.namespaceURI===tt?"math"===n&&ct[o]:Boolean(yt[n]):e.namespaceURI===nt?!(t.namespaceURI===tt&&!ct[o])&&!(t.namespaceURI===et&&!lt[o])&&!yt[n]&&(st[n]||!Tt[n]):!("application/xhtml+xml"!==ut||!it[e.namespaceURI]))}(e)?(Et(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!N(/<\/no(script|embed|frames)/i,e.innerHTML)?(Me&&e.nodeType===ee&&(t=e.textContent,m([he,ge,Te],e=>{t=E(t,e," ")}),e.textContent!==t&&(d(o.removed,{element:e.cloneNode()}),e.textContent=t)),Rt(de.afterSanitizeElements,e,null),!1):(Et(e),!0)},Ot=function(e,t,n){if(Ge&&("id"===t||"name"===t)&&(n in r||n in dt))return!1;if(xe&&!ve[t]&&N(ye,t));else if(Ce&&N(Ee,t));else if(!we[t]||ve[t]){if(!(Dt(e)&&(De.tagNameCheck instanceof RegExp&&N(De.tagNameCheck,e)||De.tagNameCheck instanceof Function&&De.tagNameCheck(e))&&(De.attributeNameCheck instanceof RegExp&&N(De.attributeNameCheck,t)||De.attributeNameCheck instanceof Function&&De.attributeNameCheck(t))||"is"===t&&De.allowCustomizedBuiltInElements&&(De.tagNameCheck instanceof RegExp&&N(De.tagNameCheck,n)||De.tagNameCheck instanceof Function&&De.tagNameCheck(n))))return!1}else if(Je[t]);else if(N(Ne,E(n,_e,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==A(n,"data:")||!Ve[e])if(Ie&&!N(Ae,E(n,_e,"")));else if(n)return!1;return!0},Dt=function(e){return"annotation-xml"!==e&&y(e,Se)},Lt=function(e){Rt(de.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||Nt(e))return;const n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:we,forceKeepAttr:void 0};let r=t.length;for(;r--;){const a=t[r],{name:l,namespaceURI:c,value:s}=a,u=pt(l),p=s;let d="value"===l?p:_(p);if(n.attrName=u,n.attrValue=d,n.keepAttr=!0,n.forceKeepAttr=void 0,Rt(de.uponSanitizeAttribute,e,n),d=n.attrValue,!Ye||"id"!==u&&"name"!==u||(At(l,e),d="user-content-"+d),Ue&&N(/((--!?|])>)|<\/(style|title)/i,d)){At(l,e);continue}if(n.forceKeepAttr)continue;if(!n.keepAttr){At(l,e);continue}if(!ke&&N(/\/>/i,d)){At(l,e);continue}Me&&m([he,ge,Te],e=>{d=E(d,e," ")});const h=pt(e.nodeName);if(Ot(h,u,d)){if(le&&"object"==typeof j&&"function"==typeof j.getAttributeType)if(c);else switch(j.getAttributeType(h,u)){case"TrustedHTML":d=le.createHTML(d);break;case"TrustedScriptURL":d=le.createScriptURL(d)}if(d!==p)try{c?e.setAttributeNS(c,l,d):e.setAttribute(l,d),Nt(e)?Et(e):f(o.removed)}catch(i){At(l,e)}}else At(l,e)}Rt(de.afterSanitizeAttributes,e,null)},vt=function e(t){let n=null;const o=St(t);for(Rt(de.beforeSanitizeShadowDOM,t,null);n=o.nextNode();)Rt(de.uponSanitizeShadowNode,n,null),wt(n),Lt(n),n.content instanceof s&&e(n.content);Rt(de.afterSanitizeShadowDOM,t,null)};return o.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,r=null,a=null,l=null;if(rt=!e,rt&&(e="\x3c!--\x3e"),"string"!=typeof e&&!bt(e)){if("function"!=typeof e.toString)throw b("toString is not a function");if("string"!=typeof(e=e.toString()))throw b("dirty is not a string, aborting")}if(!o.isSupported)return e;if(Pe||gt(t),o.removed=[],"string"==typeof e&&(Xe=!1),Xe){if(e.nodeName){const t=pt(e.nodeName);if(!be[t]||Le[t])throw b("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof R)n=_t("\x3c!----\x3e"),r=n.ownerDocument.importNode(e,!0),r.nodeType===Q&&"BODY"===r.nodeName||"HTML"===r.nodeName?n=r:n.appendChild(r);else{if(!Fe&&!Me&&!ze&&-1===e.indexOf("<"))return le&&We?le.createHTML(e):e;if(n=_t(e),!n)return Fe?null:We?ce:""}n&&He&&Et(n.firstChild);const c=St(Xe?e:n);for(;a=c.nextNode();)wt(a),Lt(a),a.content instanceof s&&vt(a.content);if(Xe)return e;if(Fe){if(Be)for(l=me.call(n.ownerDocument);n.firstChild;)l.appendChild(n.firstChild);else l=n;return(we.shadowroot||we.shadowrootmode)&&(l=fe.call(i,l,!0)),l}let u=ze?n.outerHTML:n.innerHTML;return ze&&be["!doctype"]&&n.ownerDocument&&n.ownerDocument.doctype&&n.ownerDocument.doctype.name&&N(V,n.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+n.ownerDocument.doctype.name+">\n"+u),Me&&m([he,ge,Te],e=>{u=E(u,e," ")}),le&&We?le.createHTML(u):u},o.setConfig=function(){gt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Pe=!0},o.clearConfig=function(){ft=null,Pe=!1},o.isValidAttribute=function(e,t,n){ft||gt({});const o=pt(e),r=pt(t);return Ot(o,r,n)},o.addHook=function(e,t){"function"==typeof t&&d(de[e],t)},o.removeHook=function(e,t){if(void 0!==t){const n=p(de[e],t);return-1===n?void 0:h(de[e],n,1)[0]}return f(de[e])},o.removeHooks=function(e){de[e]=[]},o.removeAllHooks=function(){de={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},o}())}}});
