System.register([],function(t,e){"use strict";return{execute:function(){function e(t,e,n,o,i){o=o||{};var r=i.internal.scaleFactor,l=i.internal.getFontSize()/r,a=l*(i.getLineHeightFactor?i.getLineHeightFactor():1.15),s="",h=1;if("middle"!==o.valign&&"bottom"!==o.valign&&"center"!==o.halign&&"right"!==o.halign||(h=(s="string"==typeof t?t.split(/\r\n|\r|\n/g):t).length||1),n+=l*(2-1.15),"middle"===o.valign?n-=h/2*a:"bottom"===o.valign&&(n-=h*a),"center"===o.halign||"right"===o.halign){var u=l;if("center"===o.halign&&(u*=.5),s&&h>=1){for(var d=0;d<s.length;d++)i.text(s[d],e-i.getStringUnitWidth(s[d])*u,n),n+=a;return i}e-=i.getStringUnitWidth(t)*u}return"justify"===o.halign?i.text(t,e,n,{maxWidth:o.maxWidth||100,align:"justify"}):i.text(t,e,n),i}t("a",function(t,e){var n=w(t,e),o=z(t,n);L(t,o)});var n={},o=function(){function t(t){this.jsPDFDocument=t,this.userStyles={textColor:t.getTextColor?this.jsPDFDocument.getTextColor():0,fontSize:t.internal.getFontSize(),fontStyle:t.internal.getFont().fontStyle,font:t.internal.getFont().fontName,lineWidth:t.getLineWidth?this.jsPDFDocument.getLineWidth():0,lineColor:t.getDrawColor?this.jsPDFDocument.getDrawColor():0}}return t.setDefaults=function(t,e){void 0===e&&(e=null),e?e.__autoTableDocumentDefaults=t:n=t},t.unifyColor=function(t){return Array.isArray(t)?t:"number"==typeof t?[t,t,t]:"string"==typeof t?[t]:null},t.prototype.applyStyles=function(e,n){var o,i,r;void 0===n&&(n=!1),e.fontStyle&&this.jsPDFDocument.setFontStyle&&this.jsPDFDocument.setFontStyle(e.fontStyle);var l=this.jsPDFDocument.internal.getFont(),a=l.fontStyle,s=l.fontName;if(e.font&&(s=e.font),e.fontStyle){a=e.fontStyle;var h=this.getFontList()[s];h&&-1===h.indexOf(a)&&this.jsPDFDocument.setFontStyle&&(this.jsPDFDocument.setFontStyle(h[0]),a=h[0])}if(this.jsPDFDocument.setFont(s,a),e.fontSize&&this.jsPDFDocument.setFontSize(e.fontSize),!n){var u=t.unifyColor(e.fillColor);u&&(o=this.jsPDFDocument).setFillColor.apply(o,u),(u=t.unifyColor(e.textColor))&&(i=this.jsPDFDocument).setTextColor.apply(i,u),(u=t.unifyColor(e.lineColor))&&(r=this.jsPDFDocument).setDrawColor.apply(r,u),"number"==typeof e.lineWidth&&this.jsPDFDocument.setLineWidth(e.lineWidth)}},t.prototype.splitTextToSize=function(t,e,n){return this.jsPDFDocument.splitTextToSize(t,e,n)},t.prototype.rect=function(t,e,n,o,i){return this.jsPDFDocument.rect(t,e,n,o,i)},t.prototype.getLastAutoTable=function(){return this.jsPDFDocument.lastAutoTable||null},t.prototype.getTextWidth=function(t){return this.jsPDFDocument.getTextWidth(t)},t.prototype.getDocument=function(){return this.jsPDFDocument},t.prototype.setPage=function(t){this.jsPDFDocument.setPage(t)},t.prototype.addPage=function(){return this.jsPDFDocument.addPage()},t.prototype.getFontList=function(){return this.jsPDFDocument.getFontList()},t.prototype.getGlobalOptions=function(){return n||{}},t.prototype.getDocumentOptions=function(){return this.jsPDFDocument.__autoTableDocumentDefaults||{}},t.prototype.pageSize=function(){var t=this.jsPDFDocument.internal.pageSize;return null==t.width&&(t={width:t.getWidth(),height:t.getHeight()}),t},t.prototype.scaleFactor=function(){return this.jsPDFDocument.internal.scaleFactor},t.prototype.getLineHeightFactor=function(){var t=this.jsPDFDocument;return t.getLineHeightFactor?t.getLineHeightFactor():1.15},t.prototype.getLineHeight=function(t){return t/this.scaleFactor()*this.getLineHeightFactor()},t.prototype.pageNumber=function(){var t=this.jsPDFDocument.internal.getCurrentPageInfo();return t?t.pageNumber:this.jsPDFDocument.internal.getNumberOfPages()},t}(),i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},i(t,e)};function r(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}"function"==typeof SuppressedError&&SuppressedError;var l=function(t){function e(e){var n=t.call(this)||this;return n._element=e,n}return r(e,t),e}(Array);function a(t,e,n){return n.applyStyles(e,!0),(Array.isArray(t)?t:[t]).map(function(t){return n.getTextWidth(t)}).reduce(function(t,e){return Math.max(t,e)},0)}function s(t,e,n,o){var i=e.settings.tableLineWidth,r=e.settings.tableLineColor;t.applyStyles({lineWidth:i,lineColor:r});var l=h(i,!1);l&&t.rect(n.x,n.y,e.getWidth(t.pageSize().width),o.y-n.y,l)}function h(t,e){var n=t>0,o=e||0===e;return n&&o?"DF":n?"S":o?"F":null}function u(t,e){var n,o,i,r;if(t=t||e,Array.isArray(t)){if(t.length>=4)return{top:t[0],right:t[1],bottom:t[2],left:t[3]};if(3===t.length)return{top:t[0],right:t[1],bottom:t[2],left:t[1]};if(2===t.length)return{top:t[0],right:t[1],bottom:t[0],left:t[1]};t=1===t.length?t[0]:e}return"object"==typeof t?("number"==typeof t.vertical&&(t.top=t.vertical,t.bottom=t.vertical),"number"==typeof t.horizontal&&(t.right=t.horizontal,t.left=t.horizontal),{left:null!==(n=t.left)&&void 0!==n?n:e,top:null!==(o=t.top)&&void 0!==o?o:e,right:null!==(i=t.right)&&void 0!==i?i:e,bottom:null!==(r=t.bottom)&&void 0!==r?r:e}):("number"!=typeof t&&(t=e),{top:t,right:t,bottom:t,left:t})}function d(t,e){var n=u(e.settings.margin,0);return t.pageSize().width-(n.left+n.right)}function c(t,e,n,o,i){var r={},l=96/72,a=f(e,function(t){return i.getComputedStyle(t).backgroundColor});null!=a&&(r.fillColor=a);var s=f(e,function(t){return i.getComputedStyle(t).color});null!=s&&(r.textColor=s);var h=function(t,e){var n=[t.paddingTop,t.paddingRight,t.paddingBottom,t.paddingLeft],o=96/(72/e),i=(parseInt(t.lineHeight)-parseInt(t.fontSize))/e/2,r=n.map(function(t){return parseInt(t||"0")/o}),l=u(r,0);return i>l.top&&(l.top=i),i>l.bottom&&(l.bottom=i),l}(o,n);h&&(r.cellPadding=h);var d="borderTopColor",c=l*n,g=o.borderTopWidth;if(o.borderBottomWidth===g&&o.borderRightWidth===g&&o.borderLeftWidth===g){var p=(parseFloat(g)||0)/c;p&&(r.lineWidth=p)}else r.lineWidth={top:(parseFloat(o.borderTopWidth)||0)/c,right:(parseFloat(o.borderRightWidth)||0)/c,bottom:(parseFloat(o.borderBottomWidth)||0)/c,left:(parseFloat(o.borderLeftWidth)||0)/c},r.lineWidth.top||(r.lineWidth.right?d="borderRightColor":r.lineWidth.bottom?d="borderBottomColor":r.lineWidth.left&&(d="borderLeftColor"));var y=f(e,function(t){return i.getComputedStyle(t)[d]});null!=y&&(r.lineColor=y);var v=["left","right","center","justify"];-1!==v.indexOf(o.textAlign)&&(r.halign=o.textAlign),-1!==(v=["middle","bottom","top"]).indexOf(o.verticalAlign)&&(r.valign=o.verticalAlign);var m=parseInt(o.fontSize||"");isNaN(m)||(r.fontSize=m/l);var w=function(t){var e="";return("bold"===t.fontWeight||"bolder"===t.fontWeight||parseInt(t.fontWeight)>=700)&&(e="bold"),"italic"!==t.fontStyle&&"oblique"!==t.fontStyle||(e+="italic"),e}(o);w&&(r.fontStyle=w);var b=(o.fontFamily||"").toLowerCase();return-1!==t.indexOf(b)&&(r.font=b),r}function f(t,e){var n=g(t,e);if(!n)return null;var o=n.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d*))?\)$/);if(!o||!Array.isArray(o))return null;var i=[parseInt(o[1]),parseInt(o[2]),parseInt(o[3])];return 0===parseInt(o[4])||isNaN(i[0])||isNaN(i[1])||isNaN(i[2])?null:i}function g(t,e){var n=e(t);return"rgba(0, 0, 0, 0)"===n||"transparent"===n||"initial"===n||"inherit"===n?null==t.parentElement?null:g(t.parentElement,e):n}function p(t,e,n,o,i){var r,l,a;void 0===o&&(o=!1),void 0===i&&(i=!1),a="string"==typeof e?n.document.querySelector(e):e;var s=Object.keys(t.getFontList()),h=t.scaleFactor(),u=[],d=[],c=[];if(!a)return console.error("Html table could not be found with input: ",e),{head:u,body:d,foot:c};for(var f=0;f<a.rows.length;f++){var g=a.rows[f],p=null===(l=null===(r=null==g?void 0:g.parentElement)||void 0===r?void 0:r.tagName)||void 0===l?void 0:l.toLowerCase(),v=y(s,h,n,g,o,i);v&&("thead"===p?u.push(v):"tfoot"===p?c.push(v):d.push(v))}return{head:u,body:d,foot:c}}function y(t,e,n,o,i,r){for(var a=new l(o),s=0;s<o.cells.length;s++){var h=o.cells[s],u=n.getComputedStyle(h);if(i||"none"!==u.display){var d=void 0;r&&(d=c(t,h,e,u,n)),a.push({rowSpan:h.rowSpan,colSpan:h.colSpan,styles:d,_element:h,content:v(h)})}}var f=n.getComputedStyle(o);if(a.length>0&&(i||"none"!==f.display))return a}function v(t){var e=t.cloneNode(!0);return e.innerHTML=e.innerHTML.replace(/\n/g,"").replace(/ +/g," "),e.innerHTML=e.innerHTML.split(/<br.*?>/).map(function(t){return t.trim()}).join("\n"),e.innerText||e.textContent||""}function m(t,e,n,o,i){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var r=Object(t),l=1;l<arguments.length;l++){var a=arguments[l];if(null!=a)for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(r[s]=a[s])}return r}function w(t,e){var n=new o(t),i=n.getDocumentOptions(),r=n.getGlobalOptions();!function(t,e,n){for(var o=0,i=[t,e,n];o<i.length;o++){var r=i[o];r&&"object"!=typeof r&&console.error("The options parameter should be of type object, is: "+typeof r),r.startY&&"number"!=typeof r.startY&&(console.error("Invalid value for startY option",r.startY),delete r.startY)}}(r,i,e);var l,a=m({},r,i,e);"undefined"!=typeof window&&(l=window);var s=function(t,e,n){for(var o={styles:{},headStyles:{},bodyStyles:{},footStyles:{},alternateRowStyles:{},columnStyles:{}},i=function(i){if("columnStyles"===i){var r=t[i],l=e[i],a=n[i];o.columnStyles=m({},r,l,a)}else{var s=[t,e,n].map(function(t){return t[i]||{}});o[i]=m({},s[0],s[1],s[2])}},r=0,l=Object.keys(o);r<l.length;r++)i(l[r]);return o}(r,i,e),h=function(t,e,n){for(var o={didParseCell:[],willDrawCell:[],didDrawCell:[],willDrawPage:[],didDrawPage:[]},i=0,r=[t,e,n];i<r.length;i++){var l=r[i];l.didParseCell&&o.didParseCell.push(l.didParseCell),l.willDrawCell&&o.willDrawCell.push(l.willDrawCell),l.didDrawCell&&o.didDrawCell.push(l.didDrawCell),l.willDrawPage&&o.willDrawPage.push(l.willDrawPage),l.didDrawPage&&o.didDrawPage.push(l.didDrawPage)}return o}(r,i,e),d=function(t,e){var n,o,i,r,l,a,s,h,d,c,f,g,p,y,v=u(e.margin,40/t.scaleFactor()),m=null!==(n=function(t,e){var n=t.getLastAutoTable(),o=t.scaleFactor(),i=t.pageNumber(),r=!1;return n&&n.startPageNumber&&(r=n.startPageNumber+n.pageNumber-1===i),"number"==typeof e?e:null!=e&&!1!==e||!r||null==(null==n?void 0:n.finalY)?null:n.finalY+20/o}(t,e.startY))&&void 0!==n?n:v.top;p=!0===e.showFoot?"everyPage":!1===e.showFoot?"never":null!==(o=e.showFoot)&&void 0!==o?o:"everyPage",y=!0===e.showHead?"everyPage":!1===e.showHead?"never":null!==(i=e.showHead)&&void 0!==i?i:"everyPage";var w=null!==(r=e.useCss)&&void 0!==r&&r,b=e.theme||(w?"plain":"striped"),x=!!e.horizontalPageBreak,S=null!==(l=e.horizontalPageBreakRepeat)&&void 0!==l?l:null;return{includeHiddenHtml:null!==(a=e.includeHiddenHtml)&&void 0!==a&&a,useCss:w,theme:b,startY:m,margin:v,pageBreak:null!==(s=e.pageBreak)&&void 0!==s?s:"auto",rowPageBreak:null!==(h=e.rowPageBreak)&&void 0!==h?h:"auto",tableWidth:null!==(d=e.tableWidth)&&void 0!==d?d:"auto",showHead:y,showFoot:p,tableLineWidth:null!==(c=e.tableLineWidth)&&void 0!==c?c:0,tableLineColor:null!==(f=e.tableLineColor)&&void 0!==f?f:200,horizontalPageBreak:x,horizontalPageBreakRepeat:S,horizontalPageBreakBehaviour:null!==(g=e.horizontalPageBreakBehaviour)&&void 0!==g?g:"afterAllRows"}}(n,a),c=function(t,e,n){var o=e.head||[],i=e.body||[],r=e.foot||[];if(e.html){var l=e.includeHiddenHtml;if(n){var a=p(t,e.html,n,l,e.useCss)||{};o=a.head||o,i=a.body||o,r=a.foot||o}else console.error("Cannot parse html in non browser environment")}var s=e.columns||function(t,e,n){var o=t[0]||e[0]||n[0]||[],i=[];return Object.keys(o).filter(function(t){return"_element"!==t}).forEach(function(t){var e,n=1;"object"!=typeof(e=Array.isArray(o)?o[parseInt(t)]:o[t])||Array.isArray(e)||(n=(null==e?void 0:e.colSpan)||1);for(var r=0;r<n;r++){var l={dataKey:Array.isArray(o)?i.length:t+(r>0?"_".concat(r):"")};i.push(l)}}),i}(o,i,r);return{columns:s,head:o,body:i,foot:r}}(n,a,l);return{id:e.tableId,content:c,hooks:h,styles:s,settings:d}}var b,x=function(t,e,n){this.table=e,this.pageNumber=e.pageNumber,this.settings=e.settings,this.cursor=n,this.doc=t.getDocument()},S=function(t){function e(e,n,o,i,r,l){var a=t.call(this,e,n,l)||this;return a.cell=o,a.row=i,a.column=r,a.section=i.section,a}return r(e,t),e}(x),W=function(){function t(t,e){this.pageNumber=1,this.id=t.id,this.settings=t.settings,this.styles=t.styles,this.hooks=t.hooks,this.columns=e.columns,this.head=e.head,this.body=e.body,this.foot=e.foot}return t.prototype.getHeadHeight=function(t){return this.head.reduce(function(e,n){return e+n.getMaxCellHeight(t)},0)},t.prototype.getFootHeight=function(t){return this.foot.reduce(function(e,n){return e+n.getMaxCellHeight(t)},0)},t.prototype.allRows=function(){return this.head.concat(this.body).concat(this.foot)},t.prototype.callCellHooks=function(t,e,n,o,i,r){for(var l=0,a=e;l<a.length;l++){var s=!1===(0,a[l])(new S(t,this,n,o,i,r));if(n.text=Array.isArray(n.text)?n.text:[n.text],s)return!1}return!0},t.prototype.callEndPageHooks=function(t,e){t.applyStyles(t.userStyles);for(var n=0,o=this.hooks.didDrawPage;n<o.length;n++)(0,o[n])(new x(t,this,e))},t.prototype.callWillDrawPageHooks=function(t,e){for(var n=0,o=this.hooks.willDrawPage;n<o.length;n++)(0,o[n])(new x(t,this,e))},t.prototype.getWidth=function(t){if("number"==typeof this.settings.tableWidth)return this.settings.tableWidth;if("wrap"===this.settings.tableWidth)return this.columns.reduce(function(t,e){return t+e.wrappedWidth},0);var e=this.settings.margin;return t-e.left-e.right},t}(),P=function(){function t(t,e,n,o,i){void 0===i&&(i=!1),this.height=0,this.raw=t,t instanceof l&&(this.raw=t._element,this.element=t._element),this.index=e,this.section=n,this.cells=o,this.spansMultiplePages=i}return t.prototype.getMaxCellHeight=function(t){var e=this;return t.reduce(function(t,n){var o;return Math.max(t,(null===(o=e.cells[n.index])||void 0===o?void 0:o.height)||0)},0)},t.prototype.hasRowSpan=function(t){var e=this;return t.filter(function(t){var n=e.cells[t.index];return!!n&&n.rowSpan>1}).length>0},t.prototype.canEntireRowFit=function(t,e){return this.getMaxCellHeight(e)<=t},t.prototype.getMinimumRowHeight=function(t,e){var n=this;return t.reduce(function(t,o){var i=n.cells[o.index];if(!i)return 0;var r=e.getLineHeight(i.styles.fontSize),l=i.padding("vertical")+r;return l>t?l:t},0)},t}(),D=function(){function t(t,e,n){var o;this.contentHeight=0,this.contentWidth=0,this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.height=0,this.x=0,this.y=0,this.styles=e,this.section=n,this.raw=t;var i=t;null==t||"object"!=typeof t||Array.isArray(t)?(this.rowSpan=1,this.colSpan=1):(this.rowSpan=t.rowSpan||1,this.colSpan=t.colSpan||1,i=null!==(o=t.content)&&void 0!==o?o:t,t._element&&(this.raw=t._element));var r=null!=i?""+i:"";this.text=r.split(/\r\n|\r|\n/g)}return t.prototype.getTextPos=function(){var t,e;if("top"===this.styles.valign)t=this.y+this.padding("top");else if("bottom"===this.styles.valign)t=this.y+this.height-this.padding("bottom");else{var n=this.height-this.padding("vertical");t=this.y+n/2+this.padding("top")}if("right"===this.styles.halign)e=this.x+this.width-this.padding("right");else if("center"===this.styles.halign){var o=this.width-this.padding("horizontal");e=this.x+o/2+this.padding("left")}else e=this.x+this.padding("left");return{x:e,y:t}},t.prototype.getContentHeight=function(t,e){void 0===e&&(e=1.15);var n=(Array.isArray(this.text)?this.text.length:1)*(this.styles.fontSize/t*e)+this.padding("vertical");return Math.max(n,this.styles.minCellHeight)},t.prototype.padding=function(t){var e=u(this.styles.cellPadding,0);return"vertical"===t?e.top+e.bottom:"horizontal"===t?e.left+e.right:e[t]},t}(),C=function(){function t(t,e,n){this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.dataKey=t,this.raw=e,this.index=n}return t.prototype.getMaxCustomCellWidth=function(t){for(var e=0,n=0,o=t.allRows();n<o.length;n++){var i=o[n].cells[this.index];i&&"number"==typeof i.styles.cellWidth&&(e=Math.max(e,i.styles.cellWidth))}return e},t}();function F(t,e){!function(t,e){var n=t.scaleFactor(),o=e.settings.horizontalPageBreak,i=d(t,e);e.allRows().forEach(function(r){for(var l=0,s=e.columns;l<s.length;l++){var h=s[l],u=r.cells[h.index];if(u){var d=e.hooks.didParseCell;e.callCellHooks(t,d,u,r,h,null);var c=u.padding("horizontal");u.contentWidth=a(u.text,u.styles,t)+c;var f=a(u.text.join(" ").split(/[^\S\u00A0]+/),u.styles,t);if(u.minReadableWidth=f+u.padding("horizontal"),"number"==typeof u.styles.cellWidth)u.minWidth=u.styles.cellWidth,u.wrappedWidth=u.styles.cellWidth;else if("wrap"===u.styles.cellWidth||!0===o)u.contentWidth>i?(u.minWidth=i,u.wrappedWidth=i):(u.minWidth=u.contentWidth,u.wrappedWidth=u.contentWidth);else{var g=10/n;u.minWidth=u.styles.minCellWidth||g,u.wrappedWidth=u.contentWidth,u.minWidth>u.wrappedWidth&&(u.wrappedWidth=u.minWidth)}}}}),e.allRows().forEach(function(t){for(var n=0,o=e.columns;n<o.length;n++){var i=o[n],r=t.cells[i.index];if(r&&1===r.colSpan)i.wrappedWidth=Math.max(i.wrappedWidth,r.wrappedWidth),i.minWidth=Math.max(i.minWidth,r.minWidth),i.minReadableWidth=Math.max(i.minReadableWidth,r.minReadableWidth);else{var l=e.styles.columnStyles[i.dataKey]||e.styles.columnStyles[i.index]||{},a=l.cellWidth||l.minCellWidth;a&&"number"==typeof a&&(i.minWidth=a,i.wrappedWidth=a)}r&&(r.colSpan>1&&!i.minWidth&&(i.minWidth=r.minWidth),r.colSpan>1&&!i.wrappedWidth&&(i.wrappedWidth=r.minWidth))}})}(t,e);var n=[],o=0;e.columns.forEach(function(t){var i=t.getMaxCustomCellWidth(e);i?t.width=i:(t.width=t.wrappedWidth,n.push(t)),o+=t.width});var i=e.getWidth(t.pageSize().width)-o;i&&(i=H(n,i,function(t){return Math.max(t.minReadableWidth,t.minWidth)})),i&&(i=H(n,i,function(t){return t.minWidth})),i=Math.abs(i),!e.settings.horizontalPageBreak&&i>.1/t.scaleFactor()&&(i=i<1?i:Math.round(i),console.warn("Of the table content, ".concat(i," units width could not fit page"))),function(t){for(var e=t.allRows(),n=0;n<e.length;n++)for(var o=e[n],i=null,r=0,l=0,a=0;a<t.columns.length;a++){var s=t.columns[a];if((l-=1)>1&&t.columns[a+1])r+=s.width,delete o.cells[s.index];else if(i){var h=i;delete o.cells[s.index],i=null,h.width=s.width+r}else{if(!(h=o.cells[s.index]))continue;if(l=h.colSpan,r=0,h.colSpan>1){i=h,r+=s.width;continue}h.width=s.width+r}}}(e),function(t,e){for(var n={count:0,height:0},o=0,i=t.allRows();o<i.length;o++){for(var r=i[o],l=0,a=t.columns;l<a.length;l++){var s=a[l],h=r.cells[s.index];if(h){e.applyStyles(h.styles,!0);var u=h.width-h.padding("horizontal");if("linebreak"===h.styles.overflow)h.text=e.splitTextToSize(h.text,u+1/e.scaleFactor(),{fontSize:h.styles.fontSize});else if("ellipsize"===h.styles.overflow)h.text=j(h.text,u,h.styles,e,"...");else if("hidden"===h.styles.overflow)h.text=j(h.text,u,h.styles,e,"");else if("function"==typeof h.styles.overflow){var d=h.styles.overflow(h.text,u);h.text="string"==typeof d?[d]:d}h.contentHeight=h.getContentHeight(e.scaleFactor(),e.getLineHeightFactor());var c=h.contentHeight/h.rowSpan;h.rowSpan>1&&n.count*n.height<c*h.rowSpan?n={height:c,count:h.rowSpan}:n&&n.count>0&&n.height>c&&(c=n.height),c>r.height&&(r.height=c)}}n.count--}}(e,t),function(t){for(var e={},n=1,o=t.allRows(),i=0;i<o.length;i++)for(var r=o[i],l=0,a=t.columns;l<a.length;l++){var s=a[l],h=e[s.index];if(n>1)n--,delete r.cells[s.index];else if(h)h.cell.height+=r.height,n=h.cell.colSpan,delete r.cells[s.index],h.left--,h.left<=1&&delete e[s.index];else{var u=r.cells[s.index];if(!u)continue;if(u.height=r.height,u.rowSpan>1){var d=o.length-i,c=u.rowSpan>d?d:u.rowSpan;e[s.index]={cell:u,left:c,row:r}}}}}(e)}function H(t,e,n){for(var o=e,i=t.reduce(function(t,e){return t+e.wrappedWidth},0),r=0;r<t.length;r++){var l=t[r],a=o*(l.wrappedWidth/i),s=l.width+a,h=n(l),u=s<h?h:s;e-=u-l.width,l.width=u}if(e=Math.round(1e10*e)/1e10){var d=t.filter(function(t){return!(e<0)||t.width>n(t)});d.length&&(e=H(d,e,n))}return e}function j(t,e,n,o,i){return t.map(function(t){return function(t,e,n,o,i){var r=1e4*o.scaleFactor();if((e=Math.ceil(e*r)/r)>=a(t,n,o))return t;for(;e<a(t+i,n,o)&&!(t.length<=1);)t=t.substring(0,t.length-1);return t.trim()+i}(t,e,n,o,i)})}function z(t,e){var n=new o(t),i=function(t,e){var n,o=t.content,i=function(t){return t.map(function(t,e){var n,o;return o="object"==typeof t&&null!==(n=t.dataKey)&&void 0!==n?n:e,new C(o,t,e)})}(o.columns);(0===o.head.length&&(n=A(i,"head"))&&o.head.push(n),0===o.foot.length)&&((n=A(i,"foot"))&&o.foot.push(n));var r=t.settings.theme,l=t.styles;return{columns:i,head:k("head",o.head,i,l,r,e),body:k("body",o.body,i,l,r,e),foot:k("foot",o.foot,i,l,r,e)}}(e,n.scaleFactor()),r=new W(e,i);return F(n,r),n.applyStyles(n.userStyles),r}function k(t,e,n,o,i,r){var l={};return e.map(function(e,a){for(var s=0,h={},u=0,d=0,c=0,f=n;c<f.length;c++){var g=f[c];if(null==l[g.index]||0===l[g.index].left)if(0===d){var p=void 0,y={};"object"!=typeof(p=Array.isArray(e)?e[g.index-u-s]:e[g.dataKey])||Array.isArray(p)||(y=(null==p?void 0:p.styles)||{});var v=T(t,g,a,i,o,r,y),m=new D(p,v,t);h[g.dataKey]=m,h[g.index]=m,d=m.colSpan-1,l[g.index]={left:m.rowSpan-1,times:d}}else d--,u++;else l[g.index].left--,d=l[g.index].times,s++}return new P(e,a,t,h)})}function A(t,e){var n={};return t.forEach(function(t){if(null!=t.raw){var o=function(t,e){if("head"===t){if("object"==typeof e)return e.header||null;if("string"==typeof e||"number"==typeof e)return e}else if("foot"===t&&"object"==typeof e)return e.footer;return null}(e,t.raw);null!=o&&(n[t.dataKey]=o)}}),Object.keys(n).length>0?n:null}function T(t,e,n,o,i,r,l){var a,s={striped:{table:{fillColor:255,textColor:80,fontStyle:"normal"},head:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},body:{},foot:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},alternateRow:{fillColor:245}},grid:{table:{fillColor:255,textColor:80,fontStyle:"normal",lineWidth:.1},head:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},body:{},foot:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},alternateRow:{}},plain:{head:{fontStyle:"bold"},foot:{fontStyle:"bold"}}}[o];"head"===t?a=i.headStyles:"body"===t?a=i.bodyStyles:"foot"===t&&(a=i.footStyles);var h=m({},s.table,s[t],i.styles,a),u=i.columnStyles[e.dataKey]||i.columnStyles[e.index]||{},d="body"===t?u:{},c="body"===t&&n%2==0?m({},s.alternateRow,i.alternateRowStyles):{},f=function(t){return{font:"helvetica",fontStyle:"normal",overflow:"linebreak",fillColor:!1,textColor:20,halign:"left",valign:"top",fontSize:10,cellPadding:5/t,lineColor:200,lineWidth:0,cellWidth:"auto",minCellHeight:0,minCellWidth:0}}(r),g=m({},f,h,c,d);return m(g,l)}function R(t,e,n){var o;void 0===n&&(n={});var i=d(t,e),r=new Map,l=[],a=[],s=[];Array.isArray(e.settings.horizontalPageBreakRepeat)?s=e.settings.horizontalPageBreakRepeat:"string"!=typeof e.settings.horizontalPageBreakRepeat&&"number"!=typeof e.settings.horizontalPageBreakRepeat||(s=[e.settings.horizontalPageBreakRepeat]),s.forEach(function(t){var n=e.columns.find(function(e){return e.dataKey===t||e.index===t});n&&!r.has(n.index)&&(r.set(n.index,!0),l.push(n.index),a.push(e.columns[n.index]),i-=n.wrappedWidth)});for(var h=!0,u=null!==(o=null==n?void 0:n.start)&&void 0!==o?o:0;u<e.columns.length;)if(r.has(u))u++;else{var c=e.columns[u].wrappedWidth;if(!(h||i>=c))break;h=!1,l.push(u),a.push(e.columns[u]),i-=c,u++}return{colIndexes:l,columns:a,lastIndex:u-1}}function L(t,e){var n=e.settings,i=n.startY,r=n.margin,l={x:r.left,y:i},a=e.getHeadHeight(e.columns)+e.getFootHeight(e.columns),h=i+r.bottom+a;"avoid"===n.pageBreak&&(h+=e.body.reduce(function(t,e){return t+e.height},0));var u=new o(t);("always"===n.pageBreak||null!=n.startY&&h>u.pageSize().height)&&(q(u),l.y=r.top),e.callWillDrawPageHooks(u,l);var d=m({},l);e.startPageNumber=u.pageNumber(),n.horizontalPageBreak?function(t,e,n,o){var i=function(t,e){for(var n=[],o=0;o<e.columns.length;o++){var i=R(t,e,{start:o});i.columns.length&&(n.push(i),o=i.lastIndex)}return n}(t,e),r=e.settings;if("afterAllRows"===r.horizontalPageBreakBehaviour)i.forEach(function(i,r){t.applyStyles(t.userStyles),r>0?K(t,e,n,o,i.columns,!0):M(t,e,o,i.columns),function(t,e,n,o,i){t.applyStyles(t.userStyles),e.body.forEach(function(r,l){var a=l===e.body.length-1;O(t,e,r,a,n,o,i)})}(t,e,n,o,i.columns),B(t,e,o,i.columns)});else for(var l=-1,a=i[0],s=function(){var r=l;if(a){t.applyStyles(t.userStyles);var s=a.columns;l>=0?K(t,e,n,o,s,!0):M(t,e,o,s),r=E(t,e,l+1,o,s),B(t,e,o,s)}var h=r-l;i.slice(1).forEach(function(i){t.applyStyles(t.userStyles),K(t,e,n,o,i.columns,!0),E(t,e,l+1,o,i.columns,h),B(t,e,o,i.columns)}),l=r};l<e.body.length-1;)s()}(u,e,d,l):(u.applyStyles(u.userStyles),"firstPage"!==n.showHead&&"everyPage"!==n.showHead||e.head.forEach(function(t){return I(u,e,t,l,e.columns)}),u.applyStyles(u.userStyles),e.body.forEach(function(t,n){var o=n===e.body.length-1;O(u,e,t,o,d,l,e.columns)}),u.applyStyles(u.userStyles),"lastPage"!==n.showFoot&&"everyPage"!==n.showFoot||e.foot.forEach(function(t){return I(u,e,t,l,e.columns)})),s(u,e,d,l),e.callEndPageHooks(u,l),e.finalY=l.y,t.lastAutoTable=e,u.applyStyles(u.userStyles)}function M(t,e,n,o){var i=e.settings;t.applyStyles(t.userStyles),"firstPage"!==i.showHead&&"everyPage"!==i.showHead||e.head.forEach(function(i){return I(t,e,i,n,o)})}function E(t,e,n,o,i,r){t.applyStyles(t.userStyles),r=null!=r?r:e.body.length;var l=Math.min(n+r,e.body.length),a=-1;return e.body.slice(n,l).forEach(function(r,l){var s=n+l===e.body.length-1,h=Y(t,e,s,o);r.canEntireRowFit(h,i)&&(I(t,e,r,o,i),a=n+l)}),a}function B(t,e,n,o){var i=e.settings;t.applyStyles(t.userStyles),"lastPage"!==i.showFoot&&"everyPage"!==i.showFoot||e.foot.forEach(function(i){return I(t,e,i,n,o)})}function N(t,e,n){var o=n.getLineHeight(t.styles.fontSize),i=t.padding("vertical"),r=Math.floor((e-i)/o);return Math.max(0,r)}function O(t,e,n,o,i,r,l){var a=Y(t,e,o,r);if(n.canEntireRowFit(a,l))I(t,e,n,r,l);else if(function(t,e,n,o){var i=t.pageSize().height,r=o.settings.margin,l=i-(r.top+r.bottom);"body"===e.section&&(l-=o.getHeadHeight(o.columns)+o.getFootHeight(o.columns));var a=e.getMinimumRowHeight(o.columns,t),s=a<n;if(a>l)return console.error("Will not be able to print row ".concat(e.index," correctly since it's minimum height is larger than page height")),!0;if(!s)return!1;var h=e.hasRowSpan(o.columns);return e.getMaxCellHeight(o.columns)>l?(h&&console.error("The content of row ".concat(e.index," will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.")),!0):!h&&"avoid"!==o.settings.rowPageBreak}(t,n,a,e)){var s=function(t,e,n,o){var i={};t.spansMultiplePages=!0,t.height=0;for(var r=0,l=0,a=n.columns;l<a.length;l++){var s=a[l];if(y=t.cells[s.index]){Array.isArray(y.text)||(y.text=[y.text]),(p=m(p=new D(y.raw,y.styles,y.section),y)).text=[];var h=N(y,e,o);y.text.length>h&&(p.text=y.text.splice(h,y.text.length));var u=o.scaleFactor(),d=o.getLineHeightFactor();y.contentHeight=y.getContentHeight(u,d),y.contentHeight>=e&&(y.contentHeight=e,p.styles.minCellHeight-=e),y.contentHeight>t.height&&(t.height=y.contentHeight),p.contentHeight=p.getContentHeight(u,d),p.contentHeight>r&&(r=p.contentHeight),i[s.index]=p}}var c=new P(t.raw,-1,t.section,i,!0);c.height=r;for(var f=0,g=n.columns;f<g.length;f++){var p,y;s=g[f],(p=c.cells[s.index])&&(p.height=c.height),(y=t.cells[s.index])&&(y.height=t.height)}return c}(n,a,e,t);I(t,e,n,r,l),K(t,e,i,r,l),O(t,e,s,o,i,r,l)}else K(t,e,i,r,l),O(t,e,n,o,i,r,l)}function I(t,n,o,i,r){i.x=n.settings.margin.left;for(var l=0,a=r;l<a.length;l++){var s=a[l],h=o.cells[s.index];if(h)if(t.applyStyles(h.styles),h.x=i.x,h.y=i.y,!1!==n.callCellHooks(t,n.hooks.willDrawCell,h,o,s,i)){_(t,h,i);var u=h.getTextPos();e(h.text,u.x,u.y,{halign:h.styles.halign,valign:h.styles.valign,maxWidth:Math.ceil(h.width-h.padding("left")-h.padding("right"))},t.getDocument()),n.callCellHooks(t,n.hooks.didDrawCell,h,o,s,i),i.x+=s.width}else i.x+=s.width;else i.x+=s.width}i.y+=o.height}function _(t,e,n){var o=e.styles;if(t.getDocument().setFillColor(t.getDocument().getFillColor()),"number"==typeof o.lineWidth){var i=h(o.lineWidth,o.fillColor);i&&t.rect(e.x,n.y,e.width,e.height,i)}else"object"==typeof o.lineWidth&&(o.fillColor&&t.rect(e.x,n.y,e.width,e.height,"F"),function(t,e,n,o){var i,r,l,a;function s(e,n,o,i,r){t.getDocument().setLineWidth(e),t.getDocument().line(n,o,i,r,"S")}o.top&&(i=n.x,r=n.y,l=n.x+e.width,a=n.y,o.right&&(l+=.5*o.right),o.left&&(i-=.5*o.left),s(o.top,i,r,l,a)),o.bottom&&(i=n.x,r=n.y+e.height,l=n.x+e.width,a=n.y+e.height,o.right&&(l+=.5*o.right),o.left&&(i-=.5*o.left),s(o.bottom,i,r,l,a)),o.left&&(i=n.x,r=n.y,l=n.x,a=n.y+e.height,o.top&&(r-=.5*o.top),o.bottom&&(a+=.5*o.bottom),s(o.left,i,r,l,a)),o.right&&(i=n.x+e.width,r=n.y,l=n.x+e.width,a=n.y+e.height,o.top&&(r-=.5*o.top),o.bottom&&(a+=.5*o.bottom),s(o.right,i,r,l,a))}(t,e,n,o.lineWidth))}function Y(t,e,n,o){var i=e.settings.margin.bottom,r=e.settings.showFoot;return("everyPage"===r||"lastPage"===r&&n)&&(i+=e.getFootHeight(e.columns)),t.pageSize().height-o.y-i}function K(t,e,n,o,i,r){void 0===i&&(i=[]),void 0===r&&(r=!1),t.applyStyles(t.userStyles),"everyPage"!==e.settings.showFoot||r||e.foot.forEach(function(n){return I(t,e,n,o,i)}),e.callEndPageHooks(t,o);var l=e.settings.margin;s(t,e,n,o),q(t),e.pageNumber++,o.x=l.left,o.y=l.top,n.y=l.top,e.callWillDrawPageHooks(t,o),"everyPage"===e.settings.showHead&&(e.head.forEach(function(n){return I(t,e,n,o,i)}),t.applyStyles(t.userStyles))}function q(t){var e=t.pageNumber();return t.setPage(e+1),t.pageNumber()===e&&(t.addPage(),!0)}try{if("undefined"!=typeof window&&window){var G=window,J=G.jsPDF||(null===(b=G.jspdf)||void 0===b?void 0:b.jsPDF);J&&function(t){t.API.autoTable=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return L(this,z(this,w(this,t[0]))),this},t.API.lastAutoTable=!1,t.API.autoTableText=function(t,n,o,i){e(t,n,o,i,this)},t.API.autoTableSetDefaults=function(t){return o.setDefaults(t,this),this},t.autoTableSetDefaults=function(t,e){o.setDefaults(t,e)},t.API.autoTableHtmlToJson=function(t,e){var n;if(void 0===e&&(e=!1),"undefined"==typeof window)return console.error("Cannot run autoTableHtmlToJson in non browser environment"),null;var i=p(new o(this),t,window,e,!1),r=i.head,l=i.body;return{columns:(null===(n=r[0])||void 0===n?void 0:n.map(function(t){return t.content}))||[],rows:l,data:l}}}(J)}}catch(U){console.error("Could not apply autoTable plugin",U)}}}});
