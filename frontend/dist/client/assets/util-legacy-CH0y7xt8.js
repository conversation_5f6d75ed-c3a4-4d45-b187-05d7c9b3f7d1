System.register(["./index-legacy-B2X8pP_9.js","./vendor-legacy-BmDVBcfe.js"],function(a,e){"use strict";var t,o,r,i,s,n,l,d,p,c,u,f,b,g,m,h,x;return{setters:[a=>{t=a._,o=a.a},a=>{r=a.c,i=a.a,s=a.k,n=a.m,l=a.v,d=a.u,p=a.t,c=a.q,u=a.s,f=a.x,b=a.F,g=a.p,m=a.n,h=a.o,x=a.y}],execute:function(){var y=document.createElement("style");y.textContent=".page-wrapper[data-v-49b68f93]{display:flex;flex-direction:column;min-height:100vh;background:linear-gradient(135deg,#f5f7fa,#c3cfe2);font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif}.content-area[data-v-49b68f93]{flex:1;display:flex;justify-content:center;align-items:flex-start;padding:20px;min-height:100vh}.util-container[data-v-49b68f93]{width:100%;max-width:800px;display:flex;flex-direction:column;gap:24px}.util-header[data-v-49b68f93]{text-align:center;margin-bottom:20px}.util-title[data-v-49b68f93]{font-size:32px;font-weight:700;color:#1d1d1f;margin:0 0 8px;letter-spacing:-.8px}.util-subtitle[data-v-49b68f93]{font-size:18px;color:#8e8e93;margin:0;font-weight:500}.tool-section[data-v-49b68f93]{background:rgba(255,255,255,.95);backdrop-filter:blur(20px);border-radius:24px;padding:30px;box-shadow:0 8px 32px rgba(0,0,0,.1),0 2px 8px rgba(0,0,0,.05);border:1px solid rgba(255,255,255,.2);transition:all .3s ease}.tool-section[data-v-49b68f93]:hover{transform:translateY(-2px);box-shadow:0 12px 40px rgba(0,0,0,.15),0 4px 12px rgba(0,0,0,.08)}.tool-header[data-v-49b68f93]{margin-bottom:24px;text-align:center}.tool-title[data-v-49b68f93]{font-size:24px;font-weight:600;color:#1d1d1f;margin:0 0 8px;letter-spacing:-.5px}.tool-description[data-v-49b68f93]{font-size:16px;color:#8e8e93;margin:0;font-weight:500}.form-group[data-v-49b68f93]{margin-bottom:20px}.form-label[data-v-49b68f93]{display:block;font-size:16px;font-weight:600;color:#1d1d1f;margin-bottom:8px}.form-input[data-v-49b68f93],.form-select[data-v-49b68f93]{width:100%;padding:16px 20px;border:2px solid #e5e5ea;border-radius:16px;font-size:17px;background:#f2f2f7;color:#1d1d1f;font-weight:500;transition:all .2s ease;box-sizing:border-box;-webkit-appearance:none;appearance:none}.form-input[data-v-49b68f93]::placeholder{color:#8e8e93;font-weight:500}.form-input[data-v-49b68f93]:focus,.form-select[data-v-49b68f93]:focus{border-color:#007aff;background:#fff;outline:none;box-shadow:0 0 0 4px rgba(0,122,255,.1)}.form-select[data-v-49b68f93]{background-image:url(\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e\");background-repeat:no-repeat;background-position:right 16px center;background-size:20px;padding-right:50px}.form-hint[data-v-49b68f93]{display:block;font-size:14px;color:#8e8e93;margin-top:4px;font-weight:400}.param-inputs[data-v-49b68f93]{display:flex;gap:12px}.param-input[data-v-49b68f93]{flex:1}.url-display[data-v-49b68f93]{display:flex;gap:8px;align-items:center}.url-input[data-v-49b68f93]{flex:1;font-family:SF Mono,Monaco,Cascadia Code,Roboto Mono,Consolas,Courier New,monospace;font-size:14px;background:#fff;border-color:#007aff}.copy-button[data-v-49b68f93]{padding:16px;border:none;border-radius:16px;background:#007aff;color:#fff;font-size:16px;cursor:pointer;transition:all .2s ease;min-width:60px;display:flex;align-items:center;justify-content:center}.copy-button[data-v-49b68f93]:hover{background:#0056cc;transform:translateY(-1px)}.copy-button[data-v-49b68f93]:active{transform:translateY(0)}.action-button[data-v-49b68f93]{width:100%;padding:16px;border:none;border-radius:16px;background:#007aff;color:#fff;font-size:18px;font-weight:600;cursor:pointer;transition:all .2s ease;letter-spacing:-.3px}.action-button[data-v-49b68f93]:hover:not(:disabled){background:#0056cc;transform:translateY(-1px)}.action-button[data-v-49b68f93]:active{transform:translateY(0)}.action-button[data-v-49b68f93]:disabled{background:#c7c7cc;cursor:not-allowed;transform:none}.action-button.secondary[data-v-49b68f93]{background:#34c759}.action-button.secondary[data-v-49b68f93]:hover:not(:disabled){background:#28a745}.qr-result[data-v-49b68f93]{margin-top:20px;text-align:center}.qr-display[data-v-49b68f93]{margin-bottom:16px;padding:20px;background:#fff;border-radius:20px;border:2px solid #e5e5ea;display:inline-block}.qr-image[data-v-49b68f93]{max-width:100%;height:auto;border-radius:12px}.qr-actions[data-v-49b68f93]{display:flex;gap:12px;justify-content:center;flex-wrap:wrap}.quick-action-button[data-v-49b68f93]{padding:14px 16px;border:2px solid #e5e5ea;border-radius:16px;background:#f2f2f7;color:#1d1d1f;font-size:16px;font-weight:600;cursor:pointer;transition:all .2s ease}.quick-action-button[data-v-49b68f93]:hover:not(:disabled){border-color:#007aff;background:rgba(0,122,255,.1);transform:translateY(-1px)}.quick-action-button[data-v-49b68f93]:disabled{opacity:.5;cursor:not-allowed;transform:none}.quick-action-button.danger[data-v-49b68f93]{border-color:#ff3b30;color:#ff3b30}.quick-action-button.danger[data-v-49b68f93]:hover:not(:disabled){background:rgba(255,59,48,.1);border-color:#ff3b30}.auth-section[data-v-49b68f93]{display:flex;flex-direction:column;gap:20px}.user-info[data-v-49b68f93]{display:flex;justify-content:space-between;align-items:center;padding:16px 20px;background:rgba(52,199,89,.1);border-radius:16px;border:1px solid rgba(52,199,89,.2)}.welcome-text[data-v-49b68f93]{margin:0;font-size:16px;color:#1d1d1f;font-weight:500}.section-subtitle[data-v-49b68f93]{font-size:18px;font-weight:600;color:#1d1d1f;margin:0 0 16px;letter-spacing:-.3px}.api-key-creation[data-v-49b68f93]{padding:20px;background:rgba(0,122,255,.05);border-radius:16px;border:1px solid rgba(0,122,255,.1)}.api-keys-list[data-v-49b68f93]{padding:20px;background:rgba(142,142,147,.05);border-radius:16px;border:1px solid rgba(142,142,147,.1)}.api-key-item[data-v-49b68f93]{display:flex;justify-content:space-between;align-items:flex-start;padding:16px;background:#fff;border-radius:12px;border:1px solid #e5e5ea;margin-bottom:12px;gap:16px}.api-key-item[data-v-49b68f93]:last-child{margin-bottom:0}.api-key-info[data-v-49b68f93]{flex:1;display:flex;flex-direction:column;gap:8px}.api-key-name[data-v-49b68f93]{font-size:16px;font-weight:600;color:#1d1d1f;display:flex;align-items:center;gap:8px}.api-key-default[data-v-49b68f93]{background:#34c759;color:#fff;font-size:11px;font-weight:600;padding:2px 8px;border-radius:12px;text-transform:uppercase;letter-spacing:.5px}.api-key-details[data-v-49b68f93]{display:flex;gap:16px;align-items:center;flex-wrap:wrap}.api-key-permission[data-v-49b68f93]{padding:4px 8px;border-radius:8px;font-size:12px;font-weight:600;text-transform:uppercase}.api-key-permission.read_only[data-v-49b68f93]{background:rgba(52,199,89,.1);color:#34c759;border:1px solid rgba(52,199,89,.2)}.api-key-permission.write_only[data-v-49b68f93]{background:rgba(255,149,0,.1);color:#ff9500;border:1px solid rgba(255,149,0,.2)}.api-key-expires[data-v-49b68f93]{font-size:12px;color:#8e8e93}.api-key-value[data-v-49b68f93]{display:flex;gap:8px;align-items:center;margin-top:8px}.api-key-input[data-v-49b68f93]{font-family:SF Mono,Monaco,Cascadia Code,Roboto Mono,Consolas,Courier New,monospace;font-size:12px;background:#f2f2f7;border-color:#e5e5ea;flex:1}.api-key-actions[data-v-49b68f93]{display:flex;gap:8px;margin-top:12px;flex-wrap:wrap}.auto-action-button[data-v-49b68f93]{padding:8px 12px;border:2px solid #e5e5ea;border-radius:12px;background:#f2f2f7;color:#1d1d1f;font-size:13px;font-weight:600;cursor:pointer;transition:all .2s ease;flex:1;min-width:80px}.auto-action-button[data-v-49b68f93]:hover{border-color:#007aff;background:rgba(0,122,255,.1);transform:translateY(-1px)}.auto-action-button[data-v-49b68f93]:active{transform:translateY(0)}.expired-keys-list[data-v-49b68f93]{padding:20px;background:rgba(255,59,48,.05);border-radius:16px;border:1px solid rgba(255,59,48,.1);margin-top:20px}.expired-notice[data-v-49b68f93]{padding:12px 16px;background:rgba(255,59,48,.1);border:1px solid rgba(255,59,48,.2);border-radius:12px;margin-bottom:16px}.expired-notice p[data-v-49b68f93]{margin:0;color:#ff3b30;font-size:14px;font-weight:500;text-align:center}.api-key-item.expired[data-v-49b68f93]{opacity:.7;background:#fafafa}.api-key-permission.expired[data-v-49b68f93]{background:rgba(142,142,147,.1);color:#8e8e93;border:1px solid rgba(142,142,147,.2)}.api-key-expires.expired[data-v-49b68f93]{color:#ff3b30;font-weight:600}.copy-button.small[data-v-49b68f93]{padding:12px;min-width:48px;font-size:14px}.action-button.small[data-v-49b68f93]{padding:8px 16px;font-size:14px;width:auto;margin-top:0}.action-button.danger[data-v-49b68f93]{background:#ff3b30}.action-button.danger[data-v-49b68f93]:hover:not(:disabled){background:#d70015}.error-message[data-v-49b68f93]{padding:12px 16px;background:rgba(255,59,48,.1);border:1px solid rgba(255,59,48,.2);border-radius:12px;color:#ff3b30;font-size:14px;font-weight:500;text-align:center}.param-section[data-v-49b68f93]{margin-top:20px;padding:20px;background:rgba(52,199,89,.05);border-radius:16px;border:1px solid rgba(52,199,89,.1)}.data-result[data-v-49b68f93]{margin-top:20px;padding:20px;background:rgba(142,142,147,.05);border-radius:16px;border:1px solid rgba(142,142,147,.1)}.data-summary[data-v-49b68f93]{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px;padding:12px 16px;background:#fff;border-radius:12px;border:1px solid #e5e5ea}.data-table-container[data-v-49b68f93]{overflow-x:auto;border-radius:12px;border:1px solid #e5e5ea;background:#fff}.data-table[data-v-49b68f93]{width:100%;border-collapse:collapse;font-size:14px}.data-table th[data-v-49b68f93],.data-table td[data-v-49b68f93]{padding:12px;text-align:left;border-bottom:1px solid #e5e5ea}.data-table th[data-v-49b68f93]{background:#f2f2f7;font-weight:600;color:#1d1d1f;position:sticky;top:0}.data-table tr:last-child td[data-v-49b68f93]{border-bottom:none}.data-note[data-v-49b68f93]{padding:12px 16px;margin:0;font-size:12px;color:#8e8e93;background:#f2f2f7;border-top:1px solid #e5e5ea}.form-textarea[data-v-49b68f93]{width:100%;padding:16px 20px;border:2px solid #e5e5ea;border-radius:16px;font-size:14px;background:#f2f2f7;color:#1d1d1f;font-weight:500;transition:all .2s ease;box-sizing:border-box;font-family:SF Mono,Monaco,Cascadia Code,Roboto Mono,Consolas,Courier New,monospace;resize:vertical}.form-textarea[data-v-49b68f93]:focus{border-color:#007aff;background:#fff;outline:none;box-shadow:0 0 0 4px rgba(0,122,255,.1)}.api-response[data-v-49b68f93]{margin-top:20px;padding:20px;background:rgba(255,149,0,.05);border-radius:16px;border:1px solid rgba(255,149,0,.1)}.response-info[data-v-49b68f93]{display:flex;gap:16px;margin-bottom:16px;padding:12px 16px;background:#fff;border-radius:12px;border:1px solid #e5e5ea}.response-status[data-v-49b68f93]{padding:4px 8px;border-radius:8px;font-size:12px;font-weight:600}.response-status.success[data-v-49b68f93]{background:rgba(52,199,89,.1);color:#34c759;border:1px solid rgba(52,199,89,.2)}.response-status.error[data-v-49b68f93]{background:rgba(255,59,48,.1);color:#ff3b30;border:1px solid rgba(255,59,48,.2)}.response-time[data-v-49b68f93]{font-size:12px;color:#8e8e93;font-weight:500}.response-body[data-v-49b68f93]{background:#1d1d1f;color:#fff;padding:16px;border-radius:12px;font-size:12px;font-family:SF Mono,Monaco,Cascadia Code,Roboto Mono,Consolas,Courier New,monospace;overflow-x:auto;white-space:pre-wrap;word-break:break-word}.generated-url[data-v-49b68f93],.generated-urls[data-v-49b68f93],.shortened-result[data-v-49b68f93]{margin-top:20px;padding:20px;background:rgba(0,122,255,.05);border-radius:16px;border:1px solid rgba(0,122,255,.2)}.url-item[data-v-49b68f93]{margin-bottom:16px}.url-item[data-v-49b68f93]:last-child{margin-bottom:0}.url-label[data-v-49b68f93]{display:flex;align-items:center;justify-content:space-between;font-size:14px;font-weight:600;color:#1d1d1f;margin-bottom:8px}.key-type-badge[data-v-49b68f93]{color:#fff;font-size:10px;font-weight:700;padding:2px 6px;border-radius:8px;text-transform:uppercase;letter-spacing:.5px}.url-actions[data-v-49b68f93]{margin-top:16px;padding-top:16px;border-top:1px solid rgba(0,122,255,.2)}@media (max-width: 768px){.util-container[data-v-49b68f93]{max-width:100%}.tool-section[data-v-49b68f93]{padding:24px;margin:0 10px}.util-title[data-v-49b68f93]{font-size:28px}.tool-title[data-v-49b68f93]{font-size:20px}.param-inputs[data-v-49b68f93]{flex-direction:column;gap:8px}.url-display[data-v-49b68f93]{flex-direction:column;gap:12px}.url-item[data-v-49b68f93]{margin-bottom:12px}.url-label[data-v-49b68f93]{font-size:14px;margin-bottom:6px}.url-actions[data-v-49b68f93]{margin-top:12px;padding-top:12px}.copy-button[data-v-49b68f93]{width:100%}.user-info[data-v-49b68f93]{flex-direction:column;gap:12px;text-align:center}.api-key-item[data-v-49b68f93]{flex-direction:column;gap:12px}.api-key-details[data-v-49b68f93]{justify-content:center}.api-key-value[data-v-49b68f93]{flex-direction:column;gap:8px}.copy-button.small[data-v-49b68f93]{width:100%}.api-key-actions[data-v-49b68f93]{flex-direction:column;gap:8px}.auto-action-button[data-v-49b68f93]{width:100%;min-width:auto}.qr-actions[data-v-49b68f93]{flex-direction:column;gap:12px}.qr-actions .action-button[data-v-49b68f93]{width:100%}.data-summary[data-v-49b68f93]{flex-direction:column;gap:12px;text-align:center}.data-table-container[data-v-49b68f93]{font-size:12px}.data-table th[data-v-49b68f93],.data-table td[data-v-49b68f93]{padding:8px}.response-info[data-v-49b68f93]{flex-direction:column;gap:8px}.form-textarea[data-v-49b68f93]{font-size:14px}.response-body[data-v-49b68f93]{font-size:11px}}@media (max-width: 430px){.content-area[data-v-49b68f93]{padding:12px}.util-container[data-v-49b68f93]{max-width:100%;margin:0}.tool-section[data-v-49b68f93]{padding:20px;margin:0 4px 16px;border-radius:16px}.util-title[data-v-49b68f93]{font-size:26px;margin-bottom:8px}.util-subtitle[data-v-49b68f93]{font-size:15px;margin-bottom:20px}.tool-title[data-v-49b68f93]{font-size:18px;margin-bottom:16px}.param-inputs[data-v-49b68f93]{flex-direction:column;gap:12px}.param-input[data-v-49b68f93]{padding:12px 16px;font-size:16px;border-radius:12px;min-height:44px}.param-label[data-v-49b68f93]{font-size:14px;margin-bottom:6px}.quick-action-button[data-v-49b68f93]{padding:14px 20px;font-size:16px;min-height:44px;border-radius:12px}.url-display[data-v-49b68f93]{flex-direction:column;gap:12px;padding:16px;border-radius:12px}.url-item[data-v-49b68f93]{margin-bottom:12px;padding:12px;background:rgba(255,255,255,.8);border-radius:12px;border:1px solid #e5e5ea}.url-label[data-v-49b68f93]{font-size:14px;font-weight:600;flex-direction:column;align-items:flex-start;gap:4px;color:#1d1d1f;margin-bottom:8px;display:block}.url-actions[data-v-49b68f93]{margin-top:16px;padding-top:16px;border-top:1px solid rgba(0,122,255,.2)}.url-text[data-v-49b68f93]{font-size:14px;word-break:break-all;line-height:1.4}.copy-button[data-v-49b68f93]{width:100%;padding:12px 16px;font-size:16px;min-height:44px;border-radius:12px}.user-info[data-v-49b68f93]{flex-direction:column;gap:12px;text-align:center;padding:16px}.user-name[data-v-49b68f93]{font-size:18px}.user-role[data-v-49b68f93]{font-size:14px}.api-key-item[data-v-49b68f93]{flex-direction:column;gap:12px;padding:16px;border-radius:12px}.api-key-details[data-v-49b68f93]{justify-content:center;text-align:center}.api-key-name[data-v-49b68f93]{font-size:16px;flex-wrap:wrap;gap:6px}.api-key-default[data-v-49b68f93]{font-size:9px;padding:1px 5px;border-radius:10px}.api-key-default[data-v-49b68f93]{font-size:10px;padding:1px 6px}.api-key-value[data-v-49b68f93]{flex-direction:column;gap:8px}.api-key-text[data-v-49b68f93]{font-size:12px;word-break:break-all;padding:8px;border-radius:8px}.copy-button.small[data-v-49b68f93]{width:100%;padding:8px 12px;font-size:14px;min-height:36px}.api-key-actions[data-v-49b68f93]{flex-direction:column;gap:8px}.auto-action-button[data-v-49b68f93]{width:100%;min-width:auto;padding:10px 16px;font-size:14px;min-height:40px;border-radius:10px}.qr-container[data-v-49b68f93]{padding:16px;text-align:center}.qr-code[data-v-49b68f93]{max-width:200px;height:auto;border-radius:12px}.qr-actions[data-v-49b68f93]{flex-direction:column;gap:12px;margin-top:16px}.qr-actions .action-button[data-v-49b68f93]{width:100%;padding:12px 16px;font-size:16px;min-height:44px;border-radius:12px}.form-group[data-v-49b68f93]{margin-bottom:16px}.form-label[data-v-49b68f93]{font-size:14px;margin-bottom:6px}.form-input[data-v-49b68f93],.form-select[data-v-49b68f93]{padding:12px 16px;font-size:16px;border-radius:12px;min-height:44px;width:100%}.action-button[data-v-49b68f93]{padding:14px 20px;font-size:16px;min-height:44px;border-radius:12px;width:100%}}@media (max-width: 480px){.content-area[data-v-49b68f93]{padding:10px}.tool-section[data-v-49b68f93]{padding:20px;margin:0 5px}.util-title[data-v-49b68f93]{font-size:24px}.util-subtitle[data-v-49b68f93]{font-size:16px}}.quick-action-section[data-v-49b68f93]{background:linear-gradient(135deg,#667eea,#764ba2);border-radius:16px;padding:20px;margin-bottom:24px;text-align:center}.quick-action-section .quick-action-button[data-v-49b68f93]{background:rgba(255,255,255,.95);color:#667eea;border:none;padding:14px 24px;border-radius:12px;font-size:16px;font-weight:600;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 12px rgba(0,0,0,.1);margin-bottom:12px;width:100%;max-width:300px}.quick-action-section .quick-action-button[data-v-49b68f93]:hover:not(:disabled){background:#fff;transform:translateY(-2px);box-shadow:0 6px 16px rgba(0,0,0,.15)}.quick-action-section .quick-action-button[data-v-49b68f93]:disabled{background:rgba(255,255,255,.5);color:rgba(102,126,234,.5);cursor:not-allowed}.quick-action-section .form-hint[data-v-49b68f93]{color:rgba(255,255,255,.9);font-size:14px;margin:0;font-weight:500}.additional-tool-section[data-v-49b68f93]{background:rgba(248,249,250,.8);border-radius:16px;padding:24px;margin-bottom:20px;border:1px solid rgba(0,0,0,.05)}.additional-tool-section .section-subtitle[data-v-49b68f93]{color:#495057;font-size:18px;font-weight:600;margin:0 0 16px;padding-bottom:8px;border-bottom:2px solid #e9ecef}.additional-tool-section .form-group[data-v-49b68f93]{margin-bottom:16px}.additional-tool-section .action-button[data-v-49b68f93]{background:linear-gradient(135deg,#28a745,#20c997);color:#fff;border:none;margin-bottom:16px}.additional-tool-section .action-button[data-v-49b68f93]:hover:not(:disabled){background:linear-gradient(135deg,#218838,#1ea085);transform:translateY(-1px)}.additional-tool-section .action-button[data-v-49b68f93]:disabled{background:#6c757d;cursor:not-allowed}\n/*$vite$:1*/",document.head.appendChild(y);const k={class:"page-wrapper"},v={class:"content-area"},w={class:"util-container"},U={class:"tool-section"},T={key:0,class:"auth-section"},A={class:"form-group"},S={class:"form-group"},P=["disabled"],K={key:0,class:"error-message"},R={key:1,class:"auth-section"},C={class:"user-info"},L={class:"welcome-text"},G={class:"api-key-creation"},I={class:"form-group"},q={class:"form-group"},E=["disabled"],z={key:0,class:"api-keys-list"},M={class:"api-key-info"},$={class:"api-key-name"},D={key:0,class:"api-key-default"},j={class:"api-key-details"},F={class:"api-key-expires"},N={class:"api-key-value"},O=["value"],W=["onClick"],_={class:"api-key-actions"},Q=["onClick","disabled"],B=["onClick"],V={key:1,class:"expired-keys-list"},Y={class:"expired-notice"},J={class:"api-key-info"},H={class:"api-key-name"},X={class:"api-key-details"},Z={class:"api-key-expires expired"},aa=["onClick"],ea=["disabled"],ta={class:"tool-section"},oa={class:"form-group"},ra={class:"param-section"},ia={class:"form-group"},sa={class:"form-group"},na={class:"form-group"},la=["value"],da={class:"form-group"},pa={key:0,class:"form-group"},ca=["value"],ua={class:"form-group"},fa={key:1,class:"form-group"},ba={class:"form-group"},ga={key:0,class:"generated-urls"},ma={class:"url-label"},ha={class:"url-display"},xa=["value"],ya=["onClick"],ka={class:"url-actions"},va={class:"tool-section"},wa={class:"quick-action-section"},Ua=["disabled"],Ta={class:"form-group"},Aa={class:"form-group"},Sa={key:0,class:"qr-result"},Pa={class:"qr-display"},Ka=["src","alt"],Ra={class:"qr-actions"},Ca=["disabled"],La={class:"tool-section"},Ga={class:"quick-action-section"},Ia=["disabled"],qa={key:0,class:"shortened-result"},Ea={class:"url-label"},za={class:"url-display"},Ma=["value"],$a=["onClick"],Da={class:"url-actions"},ja={class:"tool-section"},Fa={class:"form-group"},Na={class:"additional-tool-section"},Oa={class:"form-group"},Wa=["disabled"],_a={key:0,class:"qr-result"},Qa={class:"qr-display"},Ba=["src","alt"],Va={class:"form-group"},Ya={class:"qr-actions"},Ja=["disabled"],Ha={class:"additional-tool-section"},Xa=["disabled"],Za={key:0,class:"shortened-result"},ae={class:"url-display"},ee=["value"],te={class:"tool-section"},oe={class:"form-group"},re={key:0,class:"form-group"},ie=["disabled"],se={key:1,class:"error-message"},ne={key:2,class:"data-result"},le={class:"section-subtitle"},de={class:"data-summary"},pe={class:"data-table-container"},ce={class:"data-table"},ue={key:0,class:"data-note"},fe={class:"tool-section"},be={class:"form-group"},ge={key:0,class:"form-group"},me={key:1,class:"form-group"},he={key:2,class:"form-group"},xe=["disabled"],ye={key:3,class:"error-message"},ke={key:4,class:"api-response"},ve={class:"response-info"},we={class:"response-time"},Ue={class:"response-body"};a("default",t({name:"Util",computed:{isValidUrl(){if(!this.qrGenerator.url)return!1;const a=this.qrGenerator.url,e=a.includes("/ngaji?")||a.includes("/ngaji")||a.endsWith("/ngaji"),t=a.includes("/asrama?")||a.includes("/asrama")||a.endsWith("/asrama");return console.log("🔍 isValidUrl check for:",a),console.log("🔍 isNgajiUrl:",e),console.log("🔍 isAsramaUrl:",t),e||t},hasAbsenUrl(){if(!this.qrGenerator.url)return!1;const a=this.qrGenerator.url,e=a.includes("/ngaji")||a.includes("/asrama"),t=a.includes("/pantau")||a.includes("/stat");return e&&!t},canGenerateUrls:()=>!0},data:()=>({STORAGE_KEY:"util_auth",auth:{isLoggedIn:!1,isLoggingIn:!1,username:"",password:"",accessToken:"",loginError:""},apiKeys:[],defaultReadKey:null,defaultWriteKey:null,apiKeyForm:{name:"",expiresInDays:"30",isCreating:!1},daerahOptions:[{value:"medan-barat-tex",text:"Tex"},{value:"medan-barat-ppg",text:"Materi PPG"},{value:"medan-barat-smb",text:"Umum"},{value:"medan-barat-mbg",text:"MT/MS"}],sesiOptions:[{value:"asrama-normal",text:"Pagi Siang Malam"},{value:"asrama-padat",text:"Subuh Pagi Siang Malam"},{value:"mtms-medan-barat",text:"MT/MS"}],urlGenerator:{type:"acara",extraParams:{acara:"",lokasi:"",data:"",ph:"",sesi:"",tanggal:"",time:""}},showGeneratedUrls:!1,copyStatus:[!1,!1,!1],qrGenerator:{url:"",size:"300",customText:""},qrCodeUrl:null,urlShortener:{targetType:"absen",longUrl:"",selectedUrls:[]},shortenedUrls:[],isShortening:!1,shortUrlCopied:[],loggedApiKeyUsage:{writeKey:!1,readKey:!1,writeKeyWarning:!1,readKeyWarning:!1},dataManager:{selectedEndpoint:"",parameter:"",data:[],isLoading:!1,error:null},apiTester:{selectedEndpoint:"",method:"GET",testDataJson:"",queryParams:"",response:null,isLoading:!1,error:null},additionalTools:{manualUrl:"",qrSize:"300",shortenedManualUrl:"",isProcessing:!1,qrCodeUrl:null,customText:""},baseUrl:window.location.origin}),computed:{showAttendanceParams(){return"acara"===this.urlGenerator.type||"asrama"===this.urlGenerator.type},showMonitoringParams(){return"acara"===this.urlGenerator.type||"asrama"===this.urlGenerator.type},showStatisticsParams(){return"acara"===this.urlGenerator.type||"asrama"===this.urlGenerator.type},showAsramaParams(){return"asrama"===this.urlGenerator.type},generatedUrls(){if(!this.urlGenerator.type)return[];const a=this.baseUrl,e=this.urlGenerator.type,t=[];return"acara"===e?(t.push({label:"URL Absensi Acara",url:this.buildUrl(a+"/ngaji","attendance")}),t.push({label:"URL Pantauan Acara",url:this.buildUrl(a+"/pantau-ngaji","monitoring")}),t.push({label:"URL Statistik Acara",url:this.buildUrl(a+"/stat-ngaji","statistics")})):"asrama"===e&&(t.push({label:"URL Absensi Asrama",url:this.buildUrl(a+"/asrama","attendance")}),t.push({label:"URL Pantauan Asrama",url:this.buildUrl(a+"/pantau-asrama","monitoring")}),t.push({label:"URL Statistik Asrama",url:this.buildUrl(a+"/stat-asrama","statistics")})),t},generatedUrl(){return this.generatedUrls.length>0?this.generatedUrls[0].url:""},validApiKeys(){if(!Array.isArray(this.apiKeys))return[];const a=new Date;return this.apiKeys.filter(e=>!e.expires_at||new Date(e.expires_at)>a)},expiredApiKeys(){if(!Array.isArray(this.apiKeys))return[];const a=new Date;return this.apiKeys.filter(e=>!!e.expires_at&&new Date(e.expires_at)<=a)}},watch:{generatedUrl(a){a&&!this.qrGenerator.url&&(this.qrGenerator.url=a)}},methods:{async login(){if(this.auth.username&&this.auth.password){this.auth.isLoggingIn=!0,this.auth.loginError="";try{const e=await fetch("/api/auth/login/",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:this.auth.username,password:this.auth.password})});if(!e.ok){let t="Login gagal";try{t=(await e.json()).detail||t}catch(a){t=`Server error (${e.status}): ${e.statusText}`}throw new Error(t)}const t=await e.json();this.auth.accessToken=t.access,this.auth.isLoggedIn=!0,this.auth.password="",localStorage.setItem(this.STORAGE_KEY,JSON.stringify({username:this.auth.username,accessToken:this.auth.accessToken})),await this.loadApiKeys()}catch(e){console.error("Login error:",e),this.auth.loginError=e.message||"Gagal login. Periksa username dan password Anda."}finally{this.auth.isLoggingIn=!1}}},logout(){localStorage.removeItem(this.STORAGE_KEY),this.auth={isLoggedIn:!1,isLoggingIn:!1,username:"",password:"",accessToken:"",loginError:""},this.apiKeys=[],this.apiKeyForm={name:"",expiresInDays:"30",isCreating:!1}},isDefault(a){return this.defaultReadKey&&this.defaultReadKey.id===a.id||this.defaultWriteKey&&this.defaultWriteKey.id===a.id},setAsDefault(a){"read_only"===a.permission&&(this.defaultReadKey=a,console.log(`✅ Set "${a.name}" as default READ_ONLY key`),this.loggedApiKeyUsage.readKey=!1,this.loggedApiKeyUsage.readKeyWarning=!1),"write_only"===a.permission&&(this.defaultWriteKey=a,console.log(`✅ Set "${a.name}" as default WRITE_ONLY key`),this.loggedApiKeyUsage.writeKey=!1,this.loggedApiKeyUsage.writeKeyWarning=!1),localStorage.setItem("defaultReadKey",JSON.stringify(this.defaultReadKey)),localStorage.setItem("defaultWriteKey",JSON.stringify(this.defaultWriteKey));const e=a.permission.replace("_"," ").toUpperCase();console.log(`✅ "${a.name}" telah dijadikan default ${e} key`)},getKeyTypeForUrl:a=>a.includes("Absensi")?"write_only":(a.includes("Pantauan")||a.includes("Statistik"),"read_only"),getKeyTypeBadge(a){const e=this.getKeyTypeForUrl(a);return{type:e,color:"write_only"===e?"#ff9500":"#34c759",text:"write_only"===e?"WRITE":"READ"}},getUrlTypeBadge(a){switch(a){case"absensi":return{color:"#007aff",text:"ABSENSI"};case"pantau":return{color:"#ff9500",text:"PANTAU"};case"statistik":return{color:"#34c759",text:"STATISTIK"};default:return{color:"#8e8e93",text:"UNKNOWN"}}},getUrlTypeFromLabel:a=>a.includes("Absensi")?"absensi":a.includes("Pantauan")?"pantau":a.includes("Statistik")?"statistik":"unknown",validateDefaultKeys(){const a=this.validApiKeys;this.defaultReadKey&&(a.some(a=>a.id===this.defaultReadKey.id&&"read_only"===a.permission)||(console.warn(`🗑️ Default READ_ONLY key "${this.defaultReadKey.name}" is no longer valid - clearing`),this.defaultReadKey=null,localStorage.removeItem("defaultReadKey"))),this.defaultWriteKey&&(a.some(a=>a.id===this.defaultWriteKey.id&&"write_only"===a.permission)||(console.warn(`🗑️ Default WRITE_ONLY key "${this.defaultWriteKey.name}" is no longer valid - clearing`),this.defaultWriteKey=null,localStorage.removeItem("defaultWriteKey")))},async createApiKey(){if(this.apiKeyForm.name&&this.auth.accessToken){this.apiKeyForm.isCreating=!0;try{const e=["write_only","read_only"],t=[];for(const i of e){const e="write_only"===i?"write":"read",o={name:`${this.apiKeyForm.name}-${e}`,permission:i,expires_in_days:parseInt(this.apiKeyForm.expiresInDays,10)},r=await fetch("/api/auth/apikeys/create/",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.auth.accessToken}`},body:JSON.stringify(o)});if(!r.ok){let e=`Gagal membuat ${i} key`;try{e=(await r.json()).detail||e}catch(a){e=`Server error (${r.status}): ${r.statusText}`}throw new Error(e)}const s=await r.json();t.push(s)}this.apiKeyForm.name="",await this.loadApiKeys();const o=t.find(a=>"write_only"===a.permission),r=t.find(a=>"read_only"===a.permission);o&&this.setAsDefault(o),r&&this.setAsDefault(r),console.log("✅ Kedua API key (write & read) berhasil dibuat!")}catch(e){console.error("Create API keys error:",e),console.error(`❌ Gagal membuat API keys: ${e.message}`)}finally{this.apiKeyForm.isCreating=!1}}},async loadApiKeys(){if(this.auth.accessToken){this.apiKeys.isLoading=!0;try{const e=await fetch("/api/auth/apikeys/",{headers:{Authorization:`Bearer ${this.auth.accessToken}`}});if(!e.ok){let t="Gagal memuat API keys";try{t=(await e.json()).detail||t}catch(a){t=`Server error (${e.status}): ${e.statusText}`}throw new Error(t)}const t=await e.json();this.apiKeys=t.map(a=>({...a,copied:!1})),this.validateDefaultKeys()}catch(e){console.error("Load API keys error:",e),console.error(`❌ Gagal memuat API keys: ${e.message}`)}finally{this.apiKeys.isLoading=!1}}},async revokeApiKey(a){if(confirm(`Yakin ingin menghapus API key "${a.name}"?`))try{const t=await fetch(`/api/auth/apikeys/revoke/${a.id}/`,{method:"DELETE",headers:{Authorization:`Bearer ${this.auth.accessToken}`,"Content-Type":"application/json"}});if(!t.ok){let a="Gagal menghapus API key";try{a=(await t.json()).detail||a}catch(e){a=`Server error (${t.status}): ${t.statusText}`}throw new Error(a)}this.apiKeys=this.apiKeys.filter(e=>e.id!==a.id),this.defaultReadKey&&this.defaultReadKey.id===a.id&&(console.log(`🗑️ Cleared default READ_ONLY key: ${a.name}`),this.defaultReadKey=null,localStorage.removeItem("defaultReadKey")),this.defaultWriteKey&&this.defaultWriteKey.id===a.id&&(console.log(`🗑️ Cleared default WRITE_ONLY key: ${a.name}`),this.defaultWriteKey=null,localStorage.removeItem("defaultWriteKey")),this.urlGenerator.apiKey===a.key&&(this.urlGenerator.apiKey=""),console.log("✅ API key berhasil dihapus!")}catch(t){console.error("Revoke API key error:",t),console.error(`❌ Gagal menghapus API key: ${t.message}`)}},async copyApiKey(a){try{await navigator.clipboard.writeText(a.key),a.copied=!0,setTimeout(()=>{a.copied=!1},2e3)}catch(e){const t=this.$refs[`apiKeyInput${a.id}`][0];t&&(t.select(),document.execCommand("copy"),a.copied=!0,setTimeout(()=>{a.copied=!1},2e3))}},buildUrl(a,e){const t=new URLSearchParams,o="attendance"===e?this.defaultWriteKey:this.defaultReadKey;if(o&&o.key){t.set("key",o.key);const a="attendance"===e?"WRITE_ONLY":"READ_ONLY",r="attendance"===e?"writeKey":"readKey";this.loggedApiKeyUsage[r]||(console.log(`🔑 Using default ${a} key: ${o.name}`),this.loggedApiKeyUsage[r]=!0)}else{const a="attendance"===e?"write_only":"read_only",t="attendance"===e?"attendance (ngaji/asrama absen)":"monitoring"===e?"monitoring (pantau)":"statistics (statistik)",o="attendance"===e?"writeKeyWarning":"readKeyWarning";this.loggedApiKeyUsage[o]||(console.warn(`⚠️ No default ${a} API key set for ${t}. Please set a default key in the API Key section.`),this.loggedApiKeyUsage[o]=!0)}const r=this.urlGenerator.extraParams;return"attendance"===e?(this.addParam(t,"acara",r.acara),this.addParam(t,"lokasi",r.lokasi),this.addParam(t,"data",r.data),this.addParam(t,"ph",r.ph),"asrama"===this.urlGenerator.type&&this.addParam(t,"sesi",r.sesi)):"monitoring"===e?(this.addParam(t,"acara",r.acara),this.addParam(t,"lokasi",r.lokasi),this.addParam(t,"tanggal",r.tanggal)):"statistics"===e&&(this.addParam(t,"acara",r.acara),this.addParam(t,"lokasi",r.lokasi),this.addParam(t,"tanggal",r.tanggal),"acara"===this.urlGenerator.type&&this.addParam(t,"time",r.time),"asrama"===this.urlGenerator.type&&this.addParam(t,"sesi",r.sesi)),`${a}?${t.toString()}`},addParam(a,e,t){t&&t.trim()&&a.set(e,t.trim())},generateUrls(){console.log("🚀 Generate URLs button clicked!"),this.copyStatus=[!1,!1,!1],this.qrCodeUrl=null,this.shortUrls=[],this.showGeneratedUrls=!0;const a=this.generatedUrls.find(a=>a.label.includes("Absensi")&&(a.url.includes("/ngaji?")||a.url.includes("/asrama?")));console.log(`📱 Generated ${this.generatedUrls.length} URLs:`,this.generatedUrls.map(a=>a.label).join(", ")),a?(this.qrGenerator.url=a.url,this.generateQR(),this.shortenAllGeneratedUrls(),console.log("✅ URLs generated and sent to QR Code Generator & URL Shortener")):console.warn("⚠️ No absen URL found, but URLs are still generated")},async copyUrl(a,e){try{await navigator.clipboard.writeText(a),this.$set(this.copyStatus,e,!0),setTimeout(()=>this.$set(this.copyStatus,e,!1),2e3)}catch(t){const a=this.$refs[`generatedUrlInput${e}`];a&&a[0]&&(a[0].select(),document.execCommand("copy"),this.$set(this.copyStatus,e,!0),setTimeout(()=>this.$set(this.copyStatus,e,!1),2e3))}},async copyAllUrls(){const a=this.generatedUrls.map(a=>`${a.label}:\n${a.url}`).join("\n\n");try{await navigator.clipboard.writeText(a),this.copyStatus=[!0,!0,!0],setTimeout(()=>{this.copyStatus=[!1,!1,!1]},2e3)}catch(e){console.error("Failed to copy all URLs:",e),console.error("❌ Gagal menyalin URL. Silakan copy satu per satu.")}},generateQR(){if(console.log("🔍 generateQR called with URL:",this.qrGenerator.url),!this.qrGenerator.url)return void console.log("❌ No URL in qrGenerator");const a=this.qrGenerator.url,e=a.includes("/ngaji")||a.includes("/asrama"),t=a.includes("/pantau")||a.includes("/stat");if(console.log("🔍 URL validation - isAbsenUrl:",e,"isPantauOrStatUrl:",t),!e||t)return console.log("❌ URL validation failed - not an absen URL"),void console.warn("⚠️ QR Code hanya dapat dibuat untuk URL absensi (ngaji atau asrama)");console.log("✅ URL validation passed - generating QR code");const o=new URLSearchParams({size:`${this.qrGenerator.size}x${this.qrGenerator.size}`,data:this.qrGenerator.url,format:"png",margin:"10",color:"1d1d1f",bgcolor:"ffffff"});this.qrCodeUrl=`https://api.qrserver.com/v1/create-qr-code/?${o.toString()}`},downloadQR(){if(!this.qrCodeUrl)return;const a=document.createElement("a");a.href=this.qrCodeUrl,a.download=`qr-code-${Date.now()}.png`,document.body.appendChild(a),a.click(),document.body.removeChild(a)},async generatePDF(){if(this.qrCodeUrl&&this.qrGenerator.customText.trim())try{const{default:a}=await o(async()=>{const{default:a}=await e.import("./jspdf.es.min-legacy-CbXTx0QS.js").then(a=>a.j);return{default:a}},void 0),t=new a({orientation:"portrait",unit:"mm",format:"a5"}),r=15,i=148,s=210,n=i-2*r;t.setFont("helvetica","bold"),t.setFontSize(25);const l="ABSENSI",d=t.getTextWidth(l),p=(i-d)/2;t.text(l,p,r+20),t.setFont("helvetica","normal"),t.setFontSize(18);const c=t.splitTextToSize(this.qrGenerator.customText.trim(),n);let u=r+35;c.forEach(a=>{const e=t.getTextWidth(a),o=(i-e)/2;t.text(a,o,u),u+=8}),u+=8;const f=new Image;f.crossOrigin="anonymous",await new Promise((a,e)=>{f.onload=()=>{try{const e=8,o=1,l=Math.min(n-20,100),d=l+2*e+2*o,p=(i-d)/2;u+d>s-r&&(t.addPage(),u=r+20);const c=document.createElement("canvas"),b=c.getContext("2d"),g=400;c.width=g,c.height=g;const m=g*e/d,h=g*o/d,x=g-2*m-2*h,y=.05*g;b.fillStyle="#ffffff",b.fillRect(0,0,g,g),b.fillStyle="#f8f9fa",b.strokeStyle="#dee2e6",b.lineWidth=h;const k=(a,e,t,o,r)=>{b.beginPath(),b.moveTo(a+r,e),b.lineTo(a+t-r,e),b.quadraticCurveTo(a+t,e,a+t,e+r),b.lineTo(a+t,e+o-r),b.quadraticCurveTo(a+t,e+o,a+t-r,e+o),b.lineTo(a+r,e+o),b.quadraticCurveTo(a,e+o,a,e+o-r),b.lineTo(a,e+r),b.quadraticCurveTo(a,e,a+r,e),b.closePath()};k(0,0,g,g,y),b.fill(),b.stroke(),b.save();const v=m+h,w=m+h;k(v,w,x,x,.7*y),b.clip(),b.drawImage(f,v,w,x,x),b.restore();const U=c.toDataURL("image/png");t.addImage(U,"PNG",p,u,d,d),a()}catch(o){e(o)}},f.onerror=()=>e(new Error("Failed to load QR code image")),f.src=this.qrCodeUrl});const b=`absensi-qr-${Date.now()}.pdf`;t.save(b)}catch(a){console.error("Error generating PDF:",a),console.error(`❌ Gagal membuat PDF: ${a.message}`)}},async shortenUrl(){const a=[],e=this.generatedUrls;if(e&&e.length>0){const t=e.find(a=>a.label.includes("Absensi"));t&&a.push(t);const o=e.find(a=>a.label.includes("Pantauan")),r=e.find(a=>a.label.includes("Statistik"));o&&a.push(o),r&&a.push(r)}if(0!==a.length){this.isShortening=!0,this.shortenedUrls=[],this.shortUrlCopied=new Array(a.length).fill(!1);try{const e=this.defaultWriteKey||this.defaultReadKey;if(!e||!e.key)return void console.warn("⚠️ API key diperlukan untuk memendekkan URL. Silakan set default API key terlebih dahulu.");for(const o of a){const a=new FormData;a.append("url",o.url);const r=await fetch("/api/url/",{method:"POST",headers:{Authorization:`ApiKey ${e.key}`},body:a});if(!r.ok){let a="Gagal memendekkan URL";try{a=(await r.json()).detail||a}catch(t){a=`Server error (${r.status}): ${r.statusText}`}throw new Error(a)}const i=await r.json();this.shortenedUrls.push({label:o.label,url:`${this.baseUrl}/s/${i.url_code}`,type:this.getUrlTypeFromLabel(o.label)})}}catch(o){console.error("Error shortening URL:",o),console.error(`❌ Gagal memendekkan URL: ${o.message}`)}finally{this.isShortening=!1}}else console.warn("⚠️ Silakan generate URL terlebih dahulu")},async copyShortUrl(a){try{await navigator.clipboard.writeText(this.shortenedUrls[a].url),this.shortUrlCopied[a]=!0,setTimeout(()=>{this.shortUrlCopied[a]=!1},2e3)}catch(e){const t=this.$refs[`shortenedUrlInput${a}`][0];t&&(t.select(),document.execCommand("copy"),this.shortUrlCopied[a]=!0,setTimeout(()=>{this.shortUrlCopied[a]=!1},2e3))}},async copyAllShortUrls(){try{const a=this.urlGenerator.extraParams.acara||"Event",e=this.shortenedUrls.find(a=>a.label.includes("Absensi")),t=this.shortenedUrls.find(a=>a.label.includes("Pantau")),o=this.shortenedUrls.find(a=>a.label.includes("Statistik")),r=`----------\nLINK ABSENSI\n${a}\n\nABSENSI\n${e?e.url:"URL tidak tersedia"}\n\nPANTAU\n${t?t.url:"URL tidak tersedia"}\n\nSTATISTIK\n${o?o.url:"URL tidak tersedia"}\n----------`;await navigator.clipboard.writeText(r),console.log("✅ Format link absensi berhasil disalin!")}catch(a){console.error("❌ Gagal menyalin URL. Silakan copy satu per satu.")}},generateQRForAbsen(){if(!this.generatedUrls||0===this.generatedUrls.length)return void console.warn("⚠️ Silakan klik tombol 'Generate URLs' terlebih dahulu di bagian Generator Parameter URL.");const a=this.generatedUrls.find(a=>a.label.includes("Absensi")&&(a.url.includes("/ngaji?")||a.url.includes("/asrama?")));a?(this.qrGenerator.url=a.url,this.generateQR()):console.warn("⚠️ URL absensi tidak ditemukan. Pastikan Anda telah generate URLs dengan benar.")},shortenAllGeneratedUrls(){this.shortenUrl()},async fetchEndpointData(){if(this.dataManager.selectedEndpoint){this.dataManager.isLoading=!0,this.dataManager.error=null,this.dataManager.data=[];try{const e=this.urlGenerator.apiKey||this.getUrlParameter("key");if(!e)throw new Error("API key diperlukan untuk mengakses data. Silakan masukkan API key terlebih dahulu.");let t=`/api/${this.dataManager.selectedEndpoint}/`;this.dataManager.parameter&&(t+=`${encodeURIComponent(this.dataManager.parameter)}/`);const o=await fetch(t,{headers:{Authorization:`ApiKey ${e}`,Accept:"application/json"}});if(!o.ok){let e=`HTTP ${o.status}: ${o.statusText}`;try{e=(await o.json()).detail||e}catch(a){e=`Server error (${o.status}): ${o.statusText}`}throw new Error(e)}const r=await o.json();this.dataManager.data=Array.isArray(r)?r:[r]}catch(e){console.error("Fetch endpoint data error:",e),this.dataManager.error=e.message}finally{this.dataManager.isLoading=!1}}},exportData(){if(!this.dataManager.data.length)return;const a=JSON.stringify(this.dataManager.data,null,2),e=new Blob([a],{type:"application/json"}),t=document.createElement("a");t.href=URL.createObjectURL(e),t.download=`${this.dataManager.selectedEndpoint.replace("/","-")}-${Date.now()}.json`,document.body.appendChild(t),t.click(),document.body.removeChild(t)},async testApiEndpoint(){if(this.apiTester.selectedEndpoint){this.apiTester.isLoading=!0,this.apiTester.error=null,this.apiTester.response=null;try{const e=this.urlGenerator.apiKey||this.getUrlParameter("key");if(!e)throw new Error("API key diperlukan untuk test API. Silakan masukkan API key terlebih dahulu.");let t=`/api/${this.apiTester.selectedEndpoint}/`;"GET"===this.apiTester.method&&this.apiTester.queryParams&&(t+=`?${this.apiTester.queryParams}`);const o=Date.now(),r={method:this.apiTester.method,headers:{Authorization:`ApiKey ${e}`,Accept:"application/json"}};if("POST"===this.apiTester.method)if("url"===this.apiTester.selectedEndpoint){const e=new FormData;try{const a=JSON.parse(this.apiTester.testDataJson||"{}");Object.keys(a).forEach(t=>{e.append(t,a[t])}),r.body=e}catch(a){throw new Error("Invalid JSON format in test data")}}else r.headers["Content-Type"]="application/json",r.body=this.apiTester.testDataJson||"{}";const i=await fetch(t,r),s=Date.now();let n;try{n=await i.json()}catch(a){n=await i.text()}this.apiTester.response={status:i.status,ok:i.ok,time:s-o,data:n}}catch(e){console.error("API test error:",e),this.apiTester.error=e.message}finally{this.apiTester.isLoading=!1}}},generateQRFromManualInput(){if(!this.additionalTools.manualUrl.trim())return;const a=new URLSearchParams({size:`${this.additionalTools.qrSize}x${this.additionalTools.qrSize}`,data:this.additionalTools.manualUrl,format:"png",margin:"10",color:"1d1d1f",bgcolor:"ffffff"});this.additionalTools.qrCodeUrl=`https://api.qrserver.com/v1/create-qr-code/?${a.toString()}`},downloadAdditionalQR(){if(!this.additionalTools.qrCodeUrl)return;const a=document.createElement("a");a.href=this.additionalTools.qrCodeUrl,a.download=`additional-qr-code-${Date.now()}.png`,document.body.appendChild(a),a.click(),document.body.removeChild(a)},async generateAdditionalPDF(){if(this.additionalTools.qrCodeUrl&&this.additionalTools.customText.trim())try{const{default:a}=await o(async()=>{const{default:a}=await e.import("./jspdf.es.min-legacy-CbXTx0QS.js").then(a=>a.j);return{default:a}},void 0),t=new a({orientation:"portrait",unit:"mm",format:"a5"}),r=15,i=148,s=210,n=i-2*r;t.setFont("helvetica","bold"),t.setFontSize(48);const l="ABSENSI",d=t.getTextWidth(l),p=(i-d)/2;t.text(l,p,r+25),t.setFont("helvetica","normal"),t.setFontSize(14);const c=t.splitTextToSize(this.additionalTools.customText.trim(),n);let u=r+35;c.forEach(a=>{const e=t.getTextWidth(a),o=(i-e)/2;t.text(a,o,u),u+=7}),u+=8;const f=new Image;f.crossOrigin="anonymous",await new Promise((a,e)=>{f.onload=()=>{try{const e=8,o=1,l=Math.min(n-20,100),d=l+2*e+2*o,p=(i-d)/2;u+d>s-r&&(t.addPage(),u=r+20);const c=document.createElement("canvas"),b=c.getContext("2d"),g=400;c.width=g,c.height=g;const m=g*e/d,h=g*o/d,x=g-2*m-2*h,y=.05*g;b.fillStyle="#ffffff",b.fillRect(0,0,g,g),b.fillStyle="#f8f9fa",b.strokeStyle="#dee2e6",b.lineWidth=h;const k=(a,e,t,o,r)=>{b.beginPath(),b.moveTo(a+r,e),b.lineTo(a+t-r,e),b.quadraticCurveTo(a+t,e,a+t,e+r),b.lineTo(a+t,e+o-r),b.quadraticCurveTo(a+t,e+o,a+t-r,e+o),b.lineTo(a+r,e+o),b.quadraticCurveTo(a,e+o,a,e+o-r),b.lineTo(a,e+r),b.quadraticCurveTo(a,e,a+r,e),b.closePath()};k(0,0,g,g,y),b.fill(),b.stroke(),b.save();const v=m+h,w=m+h;k(v,w,x,x,.7*y),b.clip(),b.drawImage(f,v,w,x,x),b.restore();const U=c.toDataURL("image/png");t.addImage(U,"PNG",p,u,d,d),a()}catch(o){e(o)}},f.onerror=()=>e(new Error("Failed to load QR code image")),f.src=this.additionalTools.qrCodeUrl});const b=`additional-absensi-qr-${Date.now()}.pdf`;t.save(b)}catch(a){console.error("Error generating PDF:",a),console.error(`❌ Gagal membuat PDF: ${a.message}`)}},async shortenManualUrl(){if(this.additionalTools.manualUrl.trim()){this.additionalTools.isProcessing=!0,this.additionalTools.shortenedManualUrl="";try{const e=this.defaultWriteKey||this.defaultReadKey;if(!e||!e.key)return void console.warn("⚠️ API key diperlukan untuk memendekkan URL. Silakan set default API key terlebih dahulu.");const t=new FormData;t.append("url",this.additionalTools.manualUrl);const o=await fetch("/api/url/",{method:"POST",headers:{Authorization:`ApiKey ${e.key}`},body:t});if(!o.ok){let e="Gagal memendekkan URL";try{e=(await o.json()).detail||e}catch(a){e=`Server error (${o.status}): ${o.statusText}`}throw new Error(e)}const r=await o.json();this.additionalTools.shortenedManualUrl=`${this.baseUrl}/s/${r.url_code}`}catch(e){console.error("Error shortening manual URL:",e),console.error(`❌ Gagal memendekkan URL: ${e.message}`)}finally{this.additionalTools.isProcessing=!1}}},async copyAdditionalShortUrl(){try{await navigator.clipboard.writeText(this.additionalTools.shortenedManualUrl),this.additionalTools.copied=!0,setTimeout(()=>{this.additionalTools.copied=!1},2e3)}catch(a){const e=this.$refs.additionalShortenedUrlInput;e&&(e.select(),document.execCommand("copy"),this.additionalTools.copied=!0,setTimeout(()=>{this.additionalTools.copied=!1},2e3))}},getUrlParameter:a=>new URLSearchParams(window.location.search).get(a)||"",formatDate(a){if(!a)return"Tidak diketahui";try{return new Date(a).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}catch(e){return"Format tanggal tidak valid"}}},mounted(){document.title="Sistem Absensi Sabilillah";const a=this.getUrlParameter("key");a&&(this.urlGenerator.apiKey=a);try{const a=JSON.parse(localStorage.getItem(this.STORAGE_KEY)||"{}");a.accessToken&&(this.auth.username=a.username||"",this.auth.accessToken=a.accessToken,this.auth.isLoggedIn=!0,this.loadApiKeys().catch(()=>this.logout()))}catch(e){console.error("Error restoring auth state:",e),this.logout()}try{const a=localStorage.getItem("defaultReadKey"),e=localStorage.getItem("defaultWriteKey");a&&(this.defaultReadKey=JSON.parse(a)),e&&(this.defaultWriteKey=JSON.parse(e))}catch(e){console.error("Error restoring default API keys:",e),this.defaultReadKey=null,this.defaultWriteKey=null}}},[["render",function(a,e,t,o,y,Te){return h(),r("div",k,[i("div",v,[i("div",w,[e[108]||(e[108]=i("div",{class:"util-header"},[i("h1",{class:"util-title"},"SISTEM ABSENSI SABILILLAH"),i("p",{class:"util-subtitle"},"Kumpulan Alat Absensi")],-1)),i("div",U,[e[55]||(e[55]=i("div",{class:"tool-header"},[i("h2",{class:"tool-title"},"🔐 Manajemen API Key"),i("p",{class:"tool-description"}," Login dan kelola API key untuk akses sistem ")],-1)),y.auth.isLoggedIn?(h(),r("div",R,[i("div",C,[i("p",L,[e[46]||(e[46]=c(" Selamat datang, ",-1)),i("strong",null,p(y.auth.username),1),e[47]||(e[47]=c("! ",-1))]),i("button",{onClick:e[5]||(e[5]=(...a)=>Te.logout&&Te.logout(...a)),class:"action-button secondary small"}," Keluar ")]),i("div",G,[e[51]||(e[51]=i("h3",{class:"section-subtitle"}," Buat Pasangan API Key (Read & Write) ",-1)),e[52]||(e[52]=i("p",{class:"form-hint"}," Sistem akan membuat 2 API key sekaligus: Write-only (untuk absen ngaji/asrama) dan Read-only (untuk pantau/statistik) ",-1)),i("div",I,[e[48]||(e[48]=i("label",{class:"form-label"},"Nama API Key:",-1)),n(i("input",{"onUpdate:modelValue":e[6]||(e[6]=a=>y.apiKeyForm.name=a),type:"text",placeholder:"Contoh: Absensi-Guru-2025 (akan dibuat -write dan -read)",class:"form-input"},null,512),[[l,y.apiKeyForm.name]])]),i("div",q,[e[50]||(e[50]=i("label",{class:"form-label"},"Berlaku Selama (hari):",-1)),n(i("select",{"onUpdate:modelValue":e[7]||(e[7]=a=>y.apiKeyForm.expiresInDays=a),class:"form-select"},e[49]||(e[49]=[f('<option value="1" data-v-49b68f93>1 Hari</option><option value="3" data-v-49b68f93>3 Hari</option><option value="7" data-v-49b68f93>Seminggu</option><option value="30" data-v-49b68f93>Sebulan</option><option value="90" data-v-49b68f93>3 Bulan</option><option value="365" data-v-49b68f93>Setahun</option>',6)]),512),[[u,y.apiKeyForm.expiresInDays]])]),i("button",{onClick:e[8]||(e[8]=(...a)=>Te.createApiKey&&Te.createApiKey(...a)),class:"action-button",disabled:!y.apiKeyForm.name||y.apiKeyForm.isCreating},p(y.apiKeyForm.isCreating?"Membuat...":"Buat Read & Write Keys"),9,E)]),Te.validApiKeys.length>0?(h(),r("div",z,[e[53]||(e[53]=i("h3",{class:"section-subtitle"},"API Keys Valid Anda",-1)),(h(!0),r(b,null,g(Te.validApiKeys,a=>(h(),r("div",{class:"api-key-item",key:a.id},[i("div",M,[i("div",$,[c(p(a.name)+" ",1),Te.isDefault(a)?(h(),r("span",D,"DEFAULT")):s("",!0)]),i("div",j,[i("span",{class:m(["api-key-permission",a.permission])},p(a.permission),3),i("span",F,"Berlaku hingga: "+p(Te.formatDate(a.expires_at)),1)]),i("div",N,[i("input",{value:a.key,readonly:"",class:"form-input api-key-input",ref_for:!0,ref:"apiKeyInput"+a.id},null,8,O),i("button",{onClick:e=>Te.copyApiKey(a),class:"copy-button small"},p(a.copied?"✓":"📋"),9,W)]),i("div",_,[i("button",{onClick:e=>Te.setAsDefault(a),class:"action-button small",disabled:Te.isDefault(a)},p(Te.isDefault(a)?"Default":"Set as Default"),9,Q),i("button",{onClick:e=>Te.revokeApiKey(a),class:"action-button danger small"}," Hapus ",8,B)])])]))),128))])):s("",!0),Te.expiredApiKeys.length>0?(h(),r("div",V,[e[54]||(e[54]=i("h3",{class:"section-subtitle"},"API Keys Kedaluwarsa",-1)),i("div",Y,[i("p",null,p(Te.expiredApiKeys.length)+" API key telah kedaluwarsa dan perlu dihapus atau diperbaharui. ",1)]),(h(!0),r(b,null,g(Te.expiredApiKeys,a=>(h(),r("div",{class:"api-key-item expired",key:a.id},[i("div",J,[i("div",H,p(a.name),1),i("div",X,[i("span",{class:m(["api-key-permission expired",a.permission])},p(a.permission),3),i("span",Z,"Kedaluwarsa: "+p(Te.formatDate(a.expires_at)),1)])]),i("button",{onClick:e=>Te.revokeApiKey(a),class:"action-button danger small"}," Hapus ",8,aa)]))),128))])):s("",!0),i("button",{onClick:e[9]||(e[9]=(...a)=>Te.loadApiKeys&&Te.loadApiKeys(...a)),class:"action-button secondary",disabled:y.apiKeys.isLoading},p(y.apiKeys.isLoading?"Memuat...":"Muat Ulang API Keys"),9,ea)])):(h(),r("div",T,[i("div",A,[e[44]||(e[44]=i("label",{class:"form-label"},"Username:",-1)),n(i("input",{"onUpdate:modelValue":e[0]||(e[0]=a=>y.auth.username=a),type:"text",placeholder:"Masukkan username",class:"form-input",onKeyup:e[1]||(e[1]=d((...a)=>Te.login&&Te.login(...a),["enter"]))},null,544),[[l,y.auth.username]])]),i("div",S,[e[45]||(e[45]=i("label",{class:"form-label"},"Password:",-1)),n(i("input",{"onUpdate:modelValue":e[2]||(e[2]=a=>y.auth.password=a),type:"password",placeholder:"Masukkan password",class:"form-input",onKeyup:e[3]||(e[3]=d((...a)=>Te.login&&Te.login(...a),["enter"]))},null,544),[[l,y.auth.password]])]),i("button",{onClick:e[4]||(e[4]=(...a)=>Te.login&&Te.login(...a)),class:"action-button",disabled:!y.auth.username||!y.auth.password||y.auth.isLoggingIn},p(y.auth.isLoggingIn?"Masuk...":"Masuk"),9,P),y.auth.loginError?(h(),r("div",K,p(y.auth.loginError),1)):s("",!0)]))]),i("div",ta,[e[74]||(e[74]=i("div",{class:"tool-header"},[i("h2",{class:"tool-title"},"🔗 Generator Parameter URL"),i("p",{class:"tool-description"}," Buat URL dengan parameter untuk semua halaman sistem ")],-1)),i("div",oa,[e[57]||(e[57]=i("label",{class:"form-label"},"Pilih Jenis:",-1)),n(i("select",{"onUpdate:modelValue":e[10]||(e[10]=a=>y.urlGenerator.type=a),class:"form-select"},e[56]||(e[56]=[i("option",{value:"acara"},"🎯 Acara (Event)",-1),i("option",{value:"asrama"},"🏠 Asrama (Dormitory)",-1)]),512),[[u,y.urlGenerator.type]]),e[58]||(e[58]=i("small",{class:"form-hint"}," Akan menghasilkan 3 URL sekaligus: Absensi, Pantauan, dan Statistik ",-1))]),i("div",ra,[e[71]||(e[71]=i("h3",{class:"section-subtitle"},"Parameter URL",-1)),i("div",ia,[e[59]||(e[59]=i("label",{class:"form-label"},"Nama Acara:",-1)),n(i("input",{"onUpdate:modelValue":e[11]||(e[11]=a=>y.urlGenerator.extraParams.acara=a),type:"text",placeholder:"Contoh: Ngaji Muda-Mudi Daerah",class:"form-input",title:"Nama acara yang akan ditampilkan di halaman absensi"},null,512),[[l,y.urlGenerator.extraParams.acara]]),e[60]||(e[60]=i("small",{class:"form-hint"}," Gunakan dua spasi berturut-turut untuk membuat baris baru di tampilan ",-1))]),i("div",sa,[e[61]||(e[61]=i("label",{class:"form-label"},"Lokasi:",-1)),n(i("input",{"onUpdate:modelValue":e[12]||(e[12]=a=>y.urlGenerator.extraParams.lokasi=a),type:"text",placeholder:"Contoh: Masjid Baitul Aziz",class:"form-input",title:"Lokasi pelaksanaan acara"},null,512),[[l,y.urlGenerator.extraParams.lokasi]])]),i("div",na,[e[63]||(e[63]=i("label",{class:"form-label"},"Data Parameter:",-1)),n(i("select",{"onUpdate:modelValue":e[13]||(e[13]=a=>y.urlGenerator.extraParams.data=a),class:"form-select",title:"Parameter data untuk mengambil daftar kelompok dari API"},[e[62]||(e[62]=i("option",{value:"",disabled:""},"Pilih data parameter",-1)),(h(!0),r(b,null,g(y.daerahOptions,a=>(h(),r("option",{key:a.value,value:a.value},p(a.text),9,la))),128))],512),[[u,y.urlGenerator.extraParams.data]]),e[64]||(e[64]=i("small",{class:"form-hint"}," Parameter ini digunakan untuk mengambil data kelompok dari endpoint /api/data/daerah/ ",-1))]),i("div",da,[e[65]||(e[65]=i("label",{class:"form-label"},"Custom Placeholder (opsional):",-1)),n(i("input",{"onUpdate:modelValue":e[14]||(e[14]=a=>y.urlGenerator.extraParams.ph=a),type:"text",placeholder:"Contoh: KELOMPOK",class:"form-input",title:"Teks placeholder kustom untuk input kelompok"},null,512),[[l,y.urlGenerator.extraParams.ph]])]),Te.showAsramaParams?(h(),r("div",pa,[e[67]||(e[67]=i("label",{class:"form-label"},"Sesi (khusus asrama):",-1)),n(i("select",{"onUpdate:modelValue":e[15]||(e[15]=a=>y.urlGenerator.extraParams.sesi=a),class:"form-select",title:"Sesi untuk absensi asrama"},[e[66]||(e[66]=i("option",{value:"",disabled:""},"Pilih Sesi",-1)),(h(!0),r(b,null,g(y.sesiOptions,a=>(h(),r("option",{key:a.value,value:a.value},p(a.text),9,ca))),128))],512),[[u,y.urlGenerator.extraParams.sesi]])])):s("",!0),i("div",ua,[e[68]||(e[68]=i("label",{class:"form-label"},"Filter Tanggal (opsional):",-1)),n(i("input",{"onUpdate:modelValue":e[16]||(e[16]=a=>y.urlGenerator.extraParams.tanggal=a),type:"date",class:"form-input",title:"Filter berdasarkan tanggal untuk pantauan dan statistik"},null,512),[[l,y.urlGenerator.extraParams.tanggal]])]),"acara"===y.urlGenerator.type?(h(),r("div",fa,[e[69]||(e[69]=i("label",{class:"form-label"},"Parameter Waktu Statistik (opsional):",-1)),n(i("input",{"onUpdate:modelValue":e[17]||(e[17]=a=>y.urlGenerator.extraParams.time=a),type:"time",placeholder:"Waktu Referensi",class:"form-input",title:"Waktu referensi untuk statistik ngaji"},null,512),[[l,y.urlGenerator.extraParams.time]]),e[70]||(e[70]=i("small",{class:"form-hint"}," Parameter waktu khusus untuk halaman statistik ngaji ",-1))])):s("",!0)]),i("div",ba,[i("button",{onClick:e[18]||(e[18]=(...a)=>Te.generateUrls&&Te.generateUrls(...a)),class:"action-button primary"}," 🚀 Generate URLs "),e[72]||(e[72]=i("small",{class:"form-hint"}," Generate URLs dan kirim ke QR Code Generator & URL Shortener ",-1))]),y.showGeneratedUrls&&Te.generatedUrls.length>0?(h(),r("div",ga,[e[73]||(e[73]=i("label",{class:"form-label"},"URL yang Dihasilkan:",-1)),(h(!0),r(b,null,g(Te.generatedUrls,(a,e)=>(h(),r("div",{key:e,class:"url-item"},[i("label",ma,[c(p(a.label)+": ",1),i("span",{class:"key-type-badge",style:x({backgroundColor:Te.getKeyTypeBadge(a.label).color})},p(Te.getKeyTypeBadge(a.label).text),5)]),i("div",ha,[i("input",{value:a.url,readonly:"",class:"form-input url-input",ref_for:!0,ref:`generatedUrlInput${e}`},null,8,xa),i("button",{onClick:t=>Te.copyUrl(a.url,e),class:"copy-button"},p(y.copyStatus[e]?"✓":"📋"),9,ya)])]))),128)),i("div",ka,[i("button",{onClick:e[19]||(e[19]=(...a)=>Te.copyAllUrls&&Te.copyAllUrls(...a)),class:"action-button"}," 📋 Copy Semua URL ")])])):s("",!0)]),i("div",va,[e[80]||(e[80]=i("div",{class:"tool-header"},[i("h2",{class:"tool-title"},"📱 Generator QR Code"),i("p",{class:"tool-description"}," Buat QR code untuk URL absensi (ngaji atau asrama) ")],-1)),i("div",wa,[i("button",{onClick:e[20]||(e[20]=(...a)=>Te.generateQRForAbsen&&Te.generateQRForAbsen(...a)),class:"quick-action-button",disabled:!Te.hasAbsenUrl}," 🚀 Generate QR for Absen ",8,Ua),e[75]||(e[75]=i("small",{class:"form-hint"}," Otomatis buat QR code untuk URL absensi yang telah dihasilkan ",-1))]),i("div",Ta,[e[76]||(e[76]=i("label",{class:"form-label"},"Text Kustom untuk PDF:",-1)),n(i("input",{"onUpdate:modelValue":e[21]||(e[21]=a=>y.qrGenerator.customText=a),type:"text",placeholder:"Contoh: Kajian Pagi Sesi 1",class:"form-input"},null,512),[[l,y.qrGenerator.customText]]),e[77]||(e[77]=i("small",{class:"form-hint"},' Text ini akan muncul di bawah "ABSENSI" pada PDF ',-1))]),i("div",Aa,[e[79]||(e[79]=i("label",{class:"form-label"},"Ukuran QR Code:",-1)),n(i("select",{"onUpdate:modelValue":e[22]||(e[22]=a=>y.qrGenerator.size=a),class:"form-select"},e[78]||(e[78]=[i("option",{value:"200"},"Kecil (200x200)",-1),i("option",{value:"300"},"Sedang (300x300)",-1),i("option",{value:"400"},"Besar (400x400)",-1)]),512),[[u,y.qrGenerator.size]])]),y.qrCodeUrl?(h(),r("div",Sa,[i("div",Pa,[i("img",{src:y.qrCodeUrl,alt:"QR Code untuk "+y.qrGenerator.url,class:"qr-image"},null,8,Ka)]),i("div",Ra,[i("button",{onClick:e[23]||(e[23]=(...a)=>Te.downloadQR&&Te.downloadQR(...a)),class:"action-button secondary"}," 💾 Download QR Code "),i("button",{onClick:e[24]||(e[24]=(...a)=>Te.generatePDF&&Te.generatePDF(...a)),class:"action-button",disabled:!y.qrGenerator.customText.trim()}," 📄 Generate PDF (A5) ",8,Ca)])])):s("",!0)]),i("div",La,[e[83]||(e[83]=i("div",{class:"tool-header"},[i("h2",{class:"tool-title"},"✂️ Pemendek URL"),i("p",{class:"tool-description"}," Buat URL pendek untuk kemudahan berbagi ")],-1)),i("div",Ga,[i("button",{onClick:e[25]||(e[25]=(...a)=>Te.shortenAllGeneratedUrls&&Te.shortenAllGeneratedUrls(...a)),class:"quick-action-button",disabled:!Te.generatedUrls.length||y.isShortening},p(y.isShortening?"Memproses...":"🚀 Shorten All URLs"),9,Ia),e[81]||(e[81]=i("small",{class:"form-hint"}," Otomatis pendekkan semua URL (Absen, Pantau, Statistik) yang telah dihasilkan ",-1))]),y.shortenedUrls.length>0?(h(),r("div",qa,[e[82]||(e[82]=i("label",{class:"form-label"},"URLs Pendek:",-1)),(h(!0),r(b,null,g(y.shortenedUrls,(a,e)=>(h(),r("div",{key:e,class:"url-item"},[i("label",Ea,[c(p(a.label)+": ",1),i("span",{class:"key-type-badge",style:x({backgroundColor:Te.getUrlTypeBadge(a.type).color})},p(Te.getUrlTypeBadge(a.type).text),5)]),i("div",za,[i("input",{value:a.url,readonly:"",class:"form-input url-input",ref_for:!0,ref:`shortenedUrlInput${e}`},null,8,Ma),i("button",{onClick:a=>Te.copyShortUrl(e),class:"copy-button"},p(y.shortUrlCopied[e]?"✓":"📋"),9,$a)])]))),128)),i("div",Da,[i("button",{onClick:e[26]||(e[26]=(...a)=>Te.copyAllShortUrls&&Te.copyAllShortUrls(...a)),class:"action-button"}," 📋 Copy Semua URL Pendek ")])])):s("",!0)]),i("div",ja,[e[91]||(e[91]=i("div",{class:"tool-header"},[i("h2",{class:"tool-title"},"🛠️ Additional Tools"),i("p",{class:"tool-description"}," Tools tambahan untuk QR code dan URL shortener dengan input manual ")],-1)),i("div",Fa,[e[84]||(e[84]=i("label",{class:"form-label"},"URL Manual:",-1)),n(i("input",{"onUpdate:modelValue":e[27]||(e[27]=a=>y.additionalTools.manualUrl=a),type:"text",placeholder:"Masukkan URL yang ingin diproses",class:"form-input"},null,512),[[l,y.additionalTools.manualUrl]])]),i("div",Na,[e[88]||(e[88]=i("h3",{class:"section-subtitle"},"📱 QR Code Generator",-1)),i("div",Oa,[e[86]||(e[86]=i("label",{class:"form-label"},"Ukuran QR Code:",-1)),n(i("select",{"onUpdate:modelValue":e[28]||(e[28]=a=>y.additionalTools.qrSize=a),class:"form-select"},e[85]||(e[85]=[i("option",{value:"200"},"Kecil (200x200)",-1),i("option",{value:"300"},"Sedang (300x300)",-1),i("option",{value:"400"},"Besar (400x400)",-1)]),512),[[u,y.additionalTools.qrSize]])]),i("button",{onClick:e[29]||(e[29]=(...a)=>Te.generateQRFromManualInput&&Te.generateQRFromManualInput(...a)),class:"action-button",disabled:!y.additionalTools.manualUrl.trim()}," Buat QR Code ",8,Wa),y.additionalTools.qrCodeUrl?(h(),r("div",_a,[i("div",Qa,[i("img",{src:y.additionalTools.qrCodeUrl,alt:"QR Code untuk "+y.additionalTools.manualUrl,class:"qr-image"},null,8,Ba)]),i("div",Va,[e[87]||(e[87]=i("label",{class:"form-label"},"Teks Kustom untuk PDF:",-1)),n(i("input",{"onUpdate:modelValue":e[30]||(e[30]=a=>y.additionalTools.customText=a),type:"text",placeholder:"Masukkan teks yang akan ditampilkan di PDF",class:"form-input"},null,512),[[l,y.additionalTools.customText]])]),i("div",Ya,[i("button",{onClick:e[31]||(e[31]=(...a)=>Te.downloadAdditionalQR&&Te.downloadAdditionalQR(...a)),class:"action-button secondary"}," 💾 Download QR Code "),i("button",{onClick:e[32]||(e[32]=(...a)=>Te.generateAdditionalPDF&&Te.generateAdditionalPDF(...a)),class:"action-button",disabled:!y.additionalTools.customText.trim()}," 📄 Generate PDF ",8,Ja)])])):s("",!0)]),i("div",Ha,[e[90]||(e[90]=i("h3",{class:"section-subtitle"},"✂️ URL Shortener",-1)),i("button",{onClick:e[33]||(e[33]=(...a)=>Te.shortenManualUrl&&Te.shortenManualUrl(...a)),class:"action-button",disabled:!y.additionalTools.manualUrl.trim()||y.additionalTools.isProcessing},p(y.additionalTools.isProcessing?"Memproses...":"Pendekkan URL"),9,Xa),y.additionalTools.shortenedManualUrl?(h(),r("div",Za,[e[89]||(e[89]=i("label",{class:"form-label"},"URL Pendek:",-1)),i("div",ae,[i("input",{value:y.additionalTools.shortenedManualUrl,readonly:"",class:"form-input url-input",ref:"additionalShortenedUrlInput"},null,8,ee),i("button",{onClick:e[34]||(e[34]=(...a)=>Te.copyAdditionalShortUrl&&Te.copyAdditionalShortUrl(...a)),class:"copy-button"},p(y.additionalTools.copied?"✓":"📋"),1)])])):s("",!0)])]),i("div",te,[e[97]||(e[97]=i("div",{class:"tool-header"},[i("h2",{class:"tool-title"},"📊 Manajemen Data"),i("p",{class:"tool-description"}," Kelola data sistem dan test API endpoints ")],-1)),i("div",oe,[e[93]||(e[93]=i("label",{class:"form-label"},"Pilih Endpoint Data:",-1)),n(i("select",{"onUpdate:modelValue":e[35]||(e[35]=a=>y.dataManager.selectedEndpoint=a),class:"form-select"},e[92]||(e[92]=[f('<option value="" data-v-49b68f93>Pilih endpoint...</option><optgroup label="Data Master" data-v-49b68f93><option value="data/daerah" data-v-49b68f93>Daerah (/api/data/daerah/)</option><option value="data/sesi" data-v-49b68f93>Sesi (/api/data/sesi/)</option><option value="data/materi" data-v-49b68f93>Materi (/api/data/materi/)</option><option value="data/hobi" data-v-49b68f93>Hobi (/api/data/hobi/)</option><option value="data/kelas-sekolah" data-v-49b68f93> Kelas Sekolah (/api/data/kelas-sekolah/) </option></optgroup><optgroup label="Wilayah" data-v-49b68f93><option value="wilayah" data-v-49b68f93>Wilayah (/api/wilayah/)</option></optgroup><optgroup label="Biodata" data-v-49b68f93><option value="biodata" data-v-49b68f93>Biodata (/api/biodata/)</option></optgroup>',4)]),512),[[u,y.dataManager.selectedEndpoint]])]),y.dataManager.selectedEndpoint?(h(),r("div",re,[e[94]||(e[94]=i("label",{class:"form-label"},"Parameter Tambahan (opsional):",-1)),n(i("input",{"onUpdate:modelValue":e[36]||(e[36]=a=>y.dataManager.parameter=a),type:"text",placeholder:"Contoh: medan-timur-1 (untuk endpoint daerah)",class:"form-input"},null,512),[[l,y.dataManager.parameter]]),e[95]||(e[95]=i("small",{class:"form-hint"}," Beberapa endpoint memerlukan parameter tambahan seperti ID atau nama daerah ",-1))])):s("",!0),i("button",{onClick:e[37]||(e[37]=(...a)=>Te.fetchEndpointData&&Te.fetchEndpointData(...a)),class:"action-button",disabled:!y.dataManager.selectedEndpoint||y.dataManager.isLoading},p(y.dataManager.isLoading?"Memuat...":"Ambil Data"),9,ie),y.dataManager.error?(h(),r("div",se,p(y.dataManager.error),1)):s("",!0),y.dataManager.data&&y.dataManager.data.length>0?(h(),r("div",ne,[i("h3",le," Data dari "+p(y.dataManager.selectedEndpoint),1),i("div",de,[i("p",null,[e[96]||(e[96]=i("strong",null,"Total records:",-1)),c(" "+p(y.dataManager.data.length),1)]),i("button",{onClick:e[38]||(e[38]=(...a)=>Te.exportData&&Te.exportData(...a)),class:"action-button secondary small"}," 📥 Export JSON ")]),i("div",pe,[i("table",ce,[i("thead",null,[i("tr",null,[(h(!0),r(b,null,g(y.dataManager.data[0],(a,e)=>(h(),r("th",{key:e},p(e),1))),128))])]),i("tbody",null,[(h(!0),r(b,null,g(y.dataManager.data.slice(0,10),(a,e)=>(h(),r("tr",{key:e},[(h(!0),r(b,null,g(a,(a,e)=>(h(),r("td",{key:e},p(a),1))),128))]))),128))])]),y.dataManager.data.length>10?(h(),r("p",ue," Menampilkan 10 dari "+p(y.dataManager.data.length)+" records. Export untuk melihat semua data. ",1)):s("",!0)])])):s("",!0)]),i("div",fe,[e[107]||(e[107]=i("div",{class:"tool-header"},[i("h2",{class:"tool-title"},"🧪 Test API Endpoints"),i("p",{class:"tool-description"},"Test dan debug API endpoints sistem")],-1)),i("div",be,[e[99]||(e[99]=i("label",{class:"form-label"},"Pilih Endpoint untuk Test:",-1)),n(i("select",{"onUpdate:modelValue":e[39]||(e[39]=a=>y.apiTester.selectedEndpoint=a),class:"form-select"},e[98]||(e[98]=[f('<option value="" data-v-49b68f93>Pilih endpoint...</option><optgroup label="Absensi" data-v-49b68f93><option value="absen-pengajian" data-v-49b68f93> Absensi Pengajian (/api/absen-pengajian/) </option><option value="absen-asramaan" data-v-49b68f93> Absensi Asramaan (/api/absen-asramaan/) </option></optgroup><optgroup label="URL Shortener" data-v-49b68f93><option value="url" data-v-49b68f93>URL Shortener (/api/url/)</option></optgroup>',3)]),512),[[u,y.apiTester.selectedEndpoint]])]),y.apiTester.selectedEndpoint?(h(),r("div",ge,[e[101]||(e[101]=i("label",{class:"form-label"},"Method:",-1)),n(i("select",{"onUpdate:modelValue":e[40]||(e[40]=a=>y.apiTester.method=a),class:"form-select"},e[100]||(e[100]=[i("option",{value:"GET"},"GET",-1),i("option",{value:"POST"},"POST",-1)]),512),[[u,y.apiTester.method]])])):s("",!0),y.apiTester.selectedEndpoint&&"POST"===y.apiTester.method?(h(),r("div",me,[e[102]||(e[102]=i("label",{class:"form-label"},"Test Data (JSON):",-1)),n(i("textarea",{"onUpdate:modelValue":e[41]||(e[41]=a=>y.apiTester.testDataJson=a),class:"form-textarea",rows:"6",placeholder:'{"nama": "Test User", "acara": "Test Event"}'},null,512),[[l,y.apiTester.testDataJson]]),e[103]||(e[103]=i("small",{class:"form-hint"}," Masukkan data JSON untuk testing POST requests ",-1))])):s("",!0),y.apiTester.selectedEndpoint&&"GET"===y.apiTester.method?(h(),r("div",he,[e[104]||(e[104]=i("label",{class:"form-label"},"Query Parameters:",-1)),n(i("input",{"onUpdate:modelValue":e[42]||(e[42]=a=>y.apiTester.queryParams=a),type:"text",placeholder:"tanggal=2025-01-01&acara=Test",class:"form-input"},null,512),[[l,y.apiTester.queryParams]]),e[105]||(e[105]=i("small",{class:"form-hint"}," Format: param1=value1¶m2=value2 ",-1))])):s("",!0),i("button",{onClick:e[43]||(e[43]=(...a)=>Te.testApiEndpoint&&Te.testApiEndpoint(...a)),class:"action-button",disabled:!y.apiTester.selectedEndpoint||y.apiTester.isLoading},p(y.apiTester.isLoading?"Testing...":"Test Endpoint"),9,xe),y.apiTester.error?(h(),r("div",ye,p(y.apiTester.error),1)):s("",!0),y.apiTester.response?(h(),r("div",ke,[e[106]||(e[106]=i("h3",{class:"section-subtitle"},"Response",-1)),i("div",ve,[i("span",{class:m(["response-status",y.apiTester.response.ok?"success":"error"])}," Status: "+p(y.apiTester.response.status),3),i("span",we," Time: "+p(y.apiTester.response.time)+"ms ",1)]),i("pre",Ue,p(JSON.stringify(y.apiTester.response.data,null,2)),1)])):s("",!0)])])])])}],["__scopeId","data-v-49b68f93"]]))}}});
