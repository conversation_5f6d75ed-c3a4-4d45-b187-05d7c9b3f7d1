const API_HOST = "api.var.my.id";
const ALLOWED_PATHS = [
  "/admin",
  // Django admin access
  "/auth",
  // Authentication endpoints (Django)
  "/absen-pengajian",
  // Religious study attendance (FastAPI)
  "/absen-asramaan",
  // Dormitory attendance (FastAPI)
  "/biodata",
  // Student biodata endpoints (FastAPI)
  "/data",
  // Data endpoints (FastAPI) - covers /data/daerah, /data/sesi, /data/materi, /data/hobi, /data/kelas-sekolah
  "/url",
  // URL shortener endpoints (FastAPI)
  "/wilayah"
  // Area/region endpoints (FastAPI)
];
const index = {
  async fetch(request, env) {
    const url = new URL(request.url);
    if (url.pathname.startsWith("/s/")) {
      const urlId = url.pathname.substring(3);
      if (urlId) {
        try {
          const targetUrl = "https://".concat(API_HOST, "/url/").concat(urlId).concat(url.search);
          const response = await fetch(targetUrl, {
            method: "GET",
            headers: {
              "User-Agent": "Cloudflare-Worker/1.0"
            }
          });
          if (response.ok) {
            const data = await response.json();
            return Response.redirect(data.url, 302);
          } else {
            return new Response("URL not found", { status: 404 });
          }
        } catch (error) {
          console.error("Error fetching short URL:", error);
          return new Response("Internal Server Error", { status: 500 });
        }
      } else {
        return new Response("Bad Request: Missing URL ID", { status: 400 });
      }
    }
    if (url.pathname.startsWith("/api/")) {
      const apiPath = url.pathname.substring(4);
      const isAllowed = ALLOWED_PATHS.some(
        (prefix) => apiPath.startsWith(prefix)
      );
      if (isAllowed) {
        const targetPath = url.pathname.replace(/^\/api\//, "/");
        const targetUrl = "https://".concat(API_HOST).concat(targetPath).concat(url.search);
        return fetch(targetUrl, {
          method: request.method,
          headers: request.headers,
          body: request.body,
          redirect: "follow"
        });
      } else {
        return new Response("Forbidden: Endpoint not allowed", { status: 403 });
      }
    }
    try {
      if (env.ASSETS) {
        const assetResponse = await env.ASSETS.fetch(request);
        if (assetResponse.status !== 404) {
          return assetResponse;
        }
        const indexUrl = new URL(request.url);
        indexUrl.pathname = "/index.html";
        const indexRequest = new Request(indexUrl.toString(), {
          method: request.method,
          headers: request.headers
        });
        return env.ASSETS.fetch(indexRequest);
      }
    } catch (error) {
      console.error("Error serving assets:", error);
    }
    return fetch(request);
  }
};
export {
  index as default
};
